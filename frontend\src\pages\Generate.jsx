import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { generateCode } from '../api'
import toast from 'react-hot-toast'
import { FiDownload, FiCopy, FiFile, FiFolder, FiChevronRight, FiChevronDown, FiZap, FiLoader } from 'react-icons/fi'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'
import PageContainer from '../components/layout/PageContainer'
import { useTokensWrapper } from '../hooks/useTokensWrapper'
import { getUserProjects, chat } from '../api'
import UpgradeModal from '../components/UpgradeModal'
import axios from '../config/axios'

const Generate = () => {
  const [prompt, setPrompt] = useState('')
  const [loading, setLoading] = useState(false)
  const [generatedFiles, setGeneratedFiles] = useState([])
  const [selectedFile, setSelectedFile] = useState(null)
  const [projectId, setProjectId] = useState(null)
  const [expandedFolders, setExpandedFolders] = useState({})
  const [tabFiles, setTabFiles] = useState([])
  const [error, setError] = useState(null)
  const [progress, setProgress] = useState(0)
  const [model, setModel] = useState('basic')
  const navigate = useNavigate()
  const { refreshTokenInfo } = useTokensWrapper()
  const [projectCount, setProjectCount] = useState(0)
  const [chatPrompt, setChatPrompt] = useState('')
  const [chatLoading, setChatLoading] = useState(false)
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [upgradeError, setUpgradeError] = useState(null)

  // Modern suggestions
  const suggestions = [
    "Create a SaaS dashboard with user authentication",
    "Build a Next.js e-commerce site with Stripe integration",
    "Develop a real-time chat app with websockets",
    "Generate a REST API for a todo app in FastAPI",
    "Create a mobile-first blog with markdown support",
    "Build a project management tool with Kanban board",
    "Develop a portfolio site with dark mode toggle",
    "Generate a Python microservice with async endpoints",
    "Create a React Native app for fitness tracking",
    "Build a code snippet sharing platform"
  ]

  // Handle project generation
  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!prompt.trim()) {
      toast.error('Please enter a prompt')
      return
    }
    setLoading(true)
    setError(null)
    setGeneratedFiles([])
    setProjectId(null)
    setSelectedFile(null)
    setTabFiles([])
    setProgress(0)
    try {
      // Get the token from localStorage or sessionStorage (same as axios interceptor)
      const token = localStorage.getItem('token') || sessionStorage.getItem('token')

      if (!token) {
        throw new Error('No authentication token found. Please log in again.')
      }

      const response = await fetch('/api/stream-files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ prompt, model })
      })

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.')
        }
        throw new Error(`Failed to start generation: ${response.status} ${response.statusText}`)
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let files = []
      let projectId = null
      while (true) {
        const { value, done } = await reader.read()
        if (done) break
        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = JSON.parse(line.slice(6))
            switch (data.type) {
              case 'file':
                files.push({
                  path: data.data.file.path,
                  name: data.data.file.path.split('/').pop(),
                  content: data.data.file.content,
                  type: data.data.file.path.split('.').pop()
                })
                setProgress(data.data.progress.percentage || 0)
                break
              case 'complete':
                projectId = data.data.project.id
                break
              case 'error':
                setError(data.data.error)
                toast.error(data.data.error)
                break
              default:
                break
            }
          }
        }
      }
      setGeneratedFiles(files)
      setProjectId(projectId)
      if (files.length > 0) {
        setSelectedFile(files[0])
        setTabFiles([files[0]])
      }
      refreshTokenInfo()
      fetchProjectCount()
      toast.success('Project generated successfully!')
    } catch (error) {
      // Handle token exhaustion error
      if (error.tokenInfo) {
        setUpgradeError(error.tokenInfo)
        setShowUpgradeModal(true)
        return
      }

      setError(error.message || 'Failed to generate code')
      toast.error(error.message || 'Failed to generate code')
    } finally {
      setLoading(false)
      setProgress(0)
    }
  }

  // File explorer logic
  const toggleFolder = (folderPath) => {
    setExpandedFolders(prev => ({ ...prev, [folderPath]: !prev[folderPath] }))
  }

  const openFileTab = (file) => {
    setSelectedFile(file)
    setTabFiles((prev) => {
      if (prev.find(f => f.path === file.path)) return prev
      return [...prev, file]
    })
  }

  const closeTab = (file) => {
    setTabFiles((prev) => prev.filter(f => f.path !== file.path))
    if (selectedFile && selectedFile.path === file.path) {
      setSelectedFile(tabFiles.length > 1 ? tabFiles[0] : null)
    }
  }

  const renderFileTree = (files) => {
    const fileTree = {}
    files.forEach(file => {
      const parts = file.path.split('/')
      let current = fileTree
      parts.forEach((part, idx) => {
        if (idx === parts.length - 1) {
          current[part] = file
        } else {
          current[part] = current[part] || {}
          current = current[part]
        }
      })
    })
    const renderNode = (node, path = '') => {
      return Object.entries(node).map(([name, value]) => {
        const currentPath = path ? `${path}/${name}` : name
        if (typeof value === 'object' && !value.content) {
          const isExpanded = expandedFolders[currentPath]
          return (
            <div key={currentPath} className="pl-2">
              <div
                className="flex items-center py-1.5 px-3 rounded-xl cursor-pointer transition group hover:bg-gradient-to-r hover:from-blue-900/30 hover:to-purple-900/20"
                style={{ boxShadow: isExpanded ? '0 2px 16px 0 #1a1a2a44' : undefined }}
                onClick={() => toggleFolder(currentPath)}
              >
                {isExpanded ? <FiChevronDown className="text-cyan-400 mr-1 transition-transform group-hover:scale-110" /> : <FiChevronRight className="text-cyan-400 mr-1 transition-transform group-hover:scale-110" />}
                <FiFolder className="text-yellow-300 mr-2 text-lg group-hover:drop-shadow-glow" />
                <span className="text-gray-100 font-semibold tracking-wide group-hover:text-cyan-300 transition-colors">{name}</span>
              </div>
              {isExpanded && <div className="pl-4 border-l border-cyan-900/20 ml-1">{renderNode(value, currentPath)}</div>}
            </div>
          )
        } else {
          return (
            <div
              key={currentPath}
              className={`flex items-center py-1.5 px-3 rounded-xl cursor-pointer transition group hover:bg-gradient-to-r hover:from-blue-900/30 hover:to-purple-900/20 ${selectedFile && selectedFile.path === value.path ? 'bg-gradient-to-r from-blue-800/40 to-purple-800/30 shadow-lg ring-2 ring-cyan-500/30' : ''}`}
              onClick={() => openFileTab(value)}
              tabIndex={0}
              role="button"
              aria-label={`Open file ${name}`}
            >
              <FiFile className="text-cyan-300 mr-2 text-lg group-hover:drop-shadow-glow" />
              <span className="text-gray-200 font-mono text-sm group-hover:text-cyan-200 transition-colors">{name}</span>
            </div>
          )
        }
      })
    }
    return renderNode(fileTree)
  }

  // Download project
  const handleDownload = async () => {
    if (!projectId) {
      toast.error('No project to download')
      return
    }
    try {
      // Use axios which automatically includes the Authorization header
      const response = await axios.get(`/api/projects/${projectId}/download`, {
        responseType: 'blob'
      })

      const blob = new Blob([response.data])
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `project_${projectId}.zip`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      toast.success('Project downloaded!')
    } catch (error) {
      console.error('Download error:', error)
      toast.error('Failed to download project')
    }
  }

  // Copy code
  const handleCopy = async (content) => {
    try {
      await navigator.clipboard.writeText(content)
      toast.success('Code copied!')
    } catch {
      toast.error('Failed to copy code')
    }
  }

  // Suggestion click
  const handleSuggestionClick = (suggestion) => {
    setPrompt(suggestion)
  }

  const fetchProjectCount = async () => {
    try {
      const projects = await getUserProjects()
      setProjectCount(projects.length)
    } catch (err) {
      setProjectCount(0)
    }
  }

  return (
    <PageContainer className="p-0 font-sans" style={{ fontFamily: 'Inter, Space Grotesk, sans-serif' }}>
      <div className="flex h-[calc(100vh-6rem)] bg-gradient-to-br from-[#181a20] via-[#232526] to-[#23243a] text-white">
        {/* Sidebar Explorer */}
        <aside className="w-80 min-w-[18rem] max-w-xs bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 border-r border-cyan-900/30 shadow-2xl rounded-tr-3xl rounded-br-3xl m-4 ml-0 flex flex-col backdrop-blur-2xl" style={{ boxShadow: '0 8px 32px 0 #1a1a2a55, 0 1.5px 0 0 #00fff7' }}>
          <div className="p-6 border-b border-cyan-900/20 flex justify-between items-center">
            <h2 className="text-xl font-extrabold text-cyan-200 tracking-widest drop-shadow-glow">EXPLORER</h2>
            {generatedFiles.length > 0 && (
              <button
                onClick={handleDownload}
                className="inline-flex items-center px-3 py-1.5 text-xs bg-gradient-to-r from-cyan-500 to-blue-600 text-white rounded-xl shadow-lg hover:from-cyan-400 hover:to-blue-500 transition-all focus:outline-none focus:ring-2 focus:ring-cyan-400"
              >
                <FiDownload className="mr-1 animate-bounce" />
                Download
              </button>
            )}
          </div>
          <div className="flex-1 overflow-y-auto custom-scrollbar p-4">
            {loading ? (
              <div className="flex flex-col items-center justify-center h-full">
                <FiLoader className="animate-spin text-cyan-400 text-3xl mb-4" />
                <div className="text-cyan-200 text-base font-medium">Generating project...</div>
                <div className="w-full bg-gradient-to-r from-cyan-900/30 to-blue-900/30 rounded-full h-2 mt-4 shadow-inner">
                  <div className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300 shadow-glow" style={{ width: `${progress}%`, boxShadow: '0 0 8px 2px #00fff7cc' }} />
                </div>
              </div>
            ) : generatedFiles.length > 0 ? (
              <div>{renderFileTree(generatedFiles)}</div>
            ) : (
              <div className="text-cyan-200/60 text-base p-4">No files generated yet</div>
            )}
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 flex flex-col">
          {/* Prompt Bar */}
          <form onSubmit={handleSubmit} className="flex items-center gap-4 p-8 bg-gradient-to-r from-[#23243a]/80 to-[#181a20]/90 border-b border-cyan-900/20 shadow-xl rounded-bl-3xl">
            <input
              type="text"
              value={prompt}
              onChange={e => setPrompt(e.target.value)}
              placeholder="Describe your app or project idea..."
              className="flex-1 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 text-cyan-100 placeholder:text-cyan-400/60 rounded-xl px-6 py-4 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-semibold text-lg shadow-inner border border-cyan-900/20"
              disabled={loading}
              autoFocus
            />
            <select
              value={model}
              onChange={e => setModel(e.target.value)}
              className="bg-gradient-to-r from-cyan-900/40 to-blue-900/40 text-cyan-200 font-semibold px-4 py-3 rounded-xl border border-cyan-900/30 shadow focus:outline-none focus:ring-2 focus:ring-cyan-400 transition-all text-base"
              disabled={loading}
              style={{ minWidth: 220 }}
              aria-label="Select model"
            >
              <option value="basic">VIKKI AI </option>
              <option value="advanced">VIKKI AI Advanced </option>
            </select>
            <button
              type="submit"
              className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold px-8 py-4 rounded-xl shadow-xl transition-all text-lg flex items-center gap-2 disabled:opacity-60 focus:outline-none focus:ring-2 focus:ring-cyan-400"
              disabled={loading}
            >
              <FiZap className="text-2xl animate-pulse" />
              Generate
            </button>
          </form>

          {/* Suggestions */}
          {!loading && generatedFiles.length === 0 && (
            <div className="px-8 py-4 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 border-b border-cyan-900/20 flex flex-wrap gap-3 rounded-bl-3xl">
              {suggestions.map((suggestion, idx) => (
                <button
                  key={idx}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="text-xs bg-gradient-to-r from-cyan-900/30 to-blue-900/30 text-cyan-200 px-4 py-2 rounded-full border border-cyan-900/20 hover:bg-cyan-800/40 hover:border-cyan-400 transition-colors whitespace-nowrap shadow"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          )}

          {/* Tabs for open files */}
          {tabFiles.length > 0 && (
            <div className="flex items-center gap-2 px-8 py-2 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 border-b border-cyan-900/20 rounded-bl-3xl">
              {tabFiles.map((file, idx) => (
                <div
                  key={file.path}
                  className={`flex items-center gap-2 px-4 py-2 rounded-t-2xl cursor-pointer transition-all ${selectedFile && selectedFile.path === file.path ? 'bg-gradient-to-r from-cyan-700/40 to-blue-700/30 text-white shadow-lg ring-2 ring-cyan-400/30' : 'bg-cyan-900/10 text-cyan-200/70 hover:bg-cyan-900/30'}`}
                  onClick={() => setSelectedFile(file)}
                  tabIndex={0}
                  role="tab"
                  aria-selected={selectedFile && selectedFile.path === file.path}
                >
                  <FiFile className="text-cyan-300" />
                  <span className="font-mono text-xs">{file.name}</span>
                  <button onClick={e => { e.stopPropagation(); closeTab(file); }} className="ml-1 text-xs text-cyan-200/50 hover:text-red-400">×</button>
                </div>
              ))}
            </div>
          )}

          {/* Code Editor */}
          <section className="flex-1 overflow-auto bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-3xl m-6 shadow-2xl">
            {selectedFile ? (
              <div className="h-full flex flex-col rounded-3xl shadow-xl bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90">
                <div className="flex items-center justify-between px-8 py-4 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 border-b border-cyan-900/20 rounded-t-3xl">
                  <div className="flex items-center gap-2">
                    <FiFile className="text-cyan-300" />
                    <span className="font-mono text-lg text-cyan-100 font-bold tracking-wide drop-shadow-glow">{selectedFile.name}</span>
                  </div>
                  <button
                    onClick={() => handleCopy(selectedFile.content)}
                    className="inline-flex items-center px-4 py-2 border border-cyan-900/20 text-sm font-semibold rounded-xl text-cyan-100 bg-gradient-to-r from-cyan-900/20 to-blue-900/20 hover:bg-cyan-800/40 transition-colors shadow focus:outline-none focus:ring-2 focus:ring-cyan-400"
                  >
                    <FiCopy className="mr-2" />
                    Copy
                  </button>
                </div>
                <div className="flex-1 overflow-auto custom-scrollbar rounded-b-3xl">
                  <SyntaxHighlighter
                    language={selectedFile.name.split('.').pop() || 'javascript'}
                    style={vscDarkPlus}
                    className="h-full !bg-transparent"
                    customStyle={{ margin: 0, padding: '2rem', fontSize: '16px', lineHeight: '1.7', borderRadius: '1.5rem' }}
                  >
                    {selectedFile.content || ''}
                  </SyntaxHighlighter>
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center text-cyan-200/60 text-xl font-semibold">
                Select a file to view its contents
              </div>
            )}
          </section>

          {/* Chat-based file rework UI below code editor */}
          {selectedFile && (
            <div className="p-6 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 rounded-3xl mt-4 shadow-xl">
              <h3 className="text-lg font-bold mb-2 text-cyan-200">Chat with VIKKI AI about this file</h3>
              <form onSubmit={async e => {
                e.preventDefault()
                if (!chatPrompt.trim()) return
                setChatLoading(true)
                try {
                  const newContent = await chat(`${chatPrompt}\n\nCurrent file content:\n${selectedFile.content}`, model)
                  // Update file content in generatedFiles and tabFiles
                  setGeneratedFiles(generatedFiles.map(f => f.path === selectedFile.path ? { ...f, content: newContent } : f))
                  setTabFiles(tabFiles.map(f => f.path === selectedFile.path ? { ...f, content: newContent } : f))
                  setSelectedFile({ ...selectedFile, content: newContent })
                  toast.success('File updated by VIKKI AI!')
                  setChatPrompt('')
                } catch (err) {
                  // Handle token exhaustion error
                  if (err.tokenInfo) {
                    setUpgradeError(err.tokenInfo)
                    setShowUpgradeModal(true)
                    return
                  }

                  toast.error('Failed to update file')
                } finally {
                  setChatLoading(false)
                }
              }} className="flex gap-2 mt-2">
                <input
                  type="text"
                  value={chatPrompt}
                  onChange={e => setChatPrompt(e.target.value)}
                  placeholder="Ask VIKKI AI to rework or rewrite this file..."
                  className="flex-1 bg-gray-800 text-cyan-100 px-4 py-2 rounded-xl border border-cyan-900/30 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                  disabled={chatLoading}
                />
                <button type="submit" className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-bold px-6 py-2 rounded-xl shadow-xl disabled:opacity-60" disabled={chatLoading}>
                  {chatLoading ? 'Working...' : 'Send'}
                </button>
              </form>
            </div>
          )}
        </main>
      </div>
      <style>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #23243a;
          border-radius: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #00fff7cc;
        }
        .drop-shadow-glow {
          filter: drop-shadow(0 0 6px #00fff7cc);
        }
        .shadow-glow {
          box-shadow: 0 0 16px 2px #00fff7cc;
        }
      `}</style>

      {/* Upgrade Modal */}
      <UpgradeModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        error={upgradeError?.error || ''}
        remainingTokens={upgradeError?.remaining_tokens || 0}
        tokensNeeded={upgradeError?.tokens_needed || 0}
      />
    </PageContainer>
  )
}

export default Generate 