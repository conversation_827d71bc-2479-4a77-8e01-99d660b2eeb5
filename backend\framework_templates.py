"""
Framework-specific templates and configurations for full-stack project generation
"""

FRAMEWORK_COMBINATIONS = {
    "react_fastapi": {
        "name": "React + FastAPI",
        "frontend": "react",
        "backend": "fastapi",
        "database": "sqlite",
        "description": "Modern React frontend with FastAPI backend"
    },
    "nextjs_fastapi": {
        "name": "Next.js + FastAPI",
        "frontend": "nextjs",
        "backend": "fastapi", 
        "database": "postgresql",
        "description": "Next.js full-stack with FastAPI backend"
    },
    "react_django": {
        "name": "React + Django",
        "frontend": "react",
        "backend": "django",
        "database": "postgresql",
        "description": "React SPA with Django REST API"
    },
    "vue_express": {
        "name": "Vue.js + Express",
        "frontend": "vue",
        "backend": "express",
        "database": "mongodb",
        "description": "Vue.js frontend with Node.js Express backend"
    }
}

INTEGRATION_PATTERNS = {
    "react_fastapi": {
        "api_base_url": "http://localhost:8000",
        "cors_origins": ["http://localhost:5173"],
        "auth_flow": "JWT tokens with localStorage",
        "deployment": "Docker containers with nginx proxy"
    },
    "nextjs_fastapi": {
        "api_base_url": "/api",
        "cors_origins": ["http://localhost:3000"],
        "auth_flow": "JWT tokens with httpOnly cookies",
        "deployment": "Vercel frontend + Railway backend"
    },
    "react_django": {
        "api_base_url": "http://localhost:8000/api",
        "cors_origins": ["http://localhost:5173"],
        "auth_flow": "Django session auth + JWT",
        "deployment": "Docker containers with gunicorn"
    },
    "vue_express": {
        "api_base_url": "http://localhost:3001/api",
        "cors_origins": ["http://localhost:5173"],
        "auth_flow": "JWT tokens with Vuex store",
        "deployment": "PM2 process manager"
    }
}

SAMPLE_FEATURES = {
    "todo_app": {
        "frontend_components": [
            "TodoList", "TodoItem", "AddTodo", "TodoFilter", "TodoStats"
        ],
        "backend_endpoints": [
            "GET /todos", "POST /todos", "PUT /todos/{id}", "DELETE /todos/{id}",
            "POST /auth/login", "POST /auth/register", "GET /auth/me"
        ],
        "database_models": ["User", "Todo", "Category"],
        "features": ["CRUD operations", "User authentication", "Real-time updates", "Filtering"]
    },
    "blog_platform": {
        "frontend_components": [
            "PostList", "PostDetail", "PostEditor", "CommentSection", "UserProfile"
        ],
        "backend_endpoints": [
            "GET /posts", "POST /posts", "PUT /posts/{id}", "DELETE /posts/{id}",
            "GET /posts/{id}/comments", "POST /posts/{id}/comments",
            "POST /auth/login", "POST /auth/register"
        ],
        "database_models": ["User", "Post", "Comment", "Tag"],
        "features": ["Rich text editor", "Comments", "Tags", "User profiles", "Search"]
    },
    "ecommerce": {
        "frontend_components": [
            "ProductList", "ProductDetail", "ShoppingCart", "Checkout", "OrderHistory"
        ],
        "backend_endpoints": [
            "GET /products", "GET /products/{id}", "POST /cart/add", "POST /orders",
            "GET /orders", "POST /payments/stripe", "POST /auth/login"
        ],
        "database_models": ["User", "Product", "Order", "OrderItem", "Payment"],
        "features": ["Product catalog", "Shopping cart", "Payment processing", "Order management"]
    }
}

def get_project_structure(frameworks: dict, project_type: str = "web_app") -> dict:
    """Generate complete project structure based on frameworks and project type"""
    
    frontend = frameworks.get("frontend", "react")
    backend = frameworks.get("backend", "fastapi")
    database = frameworks.get("database", "sqlite")
    
    structure = {
        "root_files": [
            "README.md",
            ".gitignore",
            "docker-compose.yml"
        ],
        "frontend_structure": get_frontend_structure(frontend),
        "backend_structure": get_backend_structure(backend, database),
        "integration": get_integration_config(frontend, backend)
    }
    
    return structure

def get_frontend_structure(framework: str) -> dict:
    """Get frontend-specific file structure"""
    structures = {
        "react": {
            "folder": "frontend",
            "files": [
                "package.json", "vite.config.ts", "tailwind.config.js", "postcss.config.js",
                "tsconfig.json", "index.html", "src/main.tsx", "src/App.tsx", "src/index.css",
                "src/components/ui/Button.tsx", "src/components/ui/Input.tsx", "src/components/ui/Modal.tsx",
                "src/components/layout/Header.tsx", "src/components/layout/Footer.tsx", "src/components/layout/Sidebar.tsx",
                "src/pages/Home.tsx", "src/pages/Login.tsx", "src/pages/Dashboard.tsx",
                "src/hooks/useAuth.ts", "src/hooks/useApi.ts", "src/hooks/useLocalStorage.ts",
                "src/utils/api.ts", "src/utils/auth.ts", "src/utils/constants.ts",
                "src/types/index.ts", "src/types/api.ts", "src/types/auth.ts",
                "src/context/AuthContext.tsx", "src/context/ThemeContext.tsx"
            ]
        },
        "nextjs": {
            "folder": "frontend",
            "files": [
                "package.json", "next.config.js", "tailwind.config.js", "postcss.config.js",
                "tsconfig.json", "app/layout.tsx", "app/page.tsx", "app/globals.css",
                "app/login/page.tsx", "app/dashboard/page.tsx", "app/api/auth/route.ts",
                "components/ui/Button.tsx", "components/ui/Input.tsx", "components/ui/Modal.tsx",
                "components/layout/Header.tsx", "components/layout/Footer.tsx", "components/layout/Sidebar.tsx",
                "lib/auth.ts", "lib/api.ts", "lib/utils.ts", "lib/constants.ts",
                "types/index.ts", "types/api.ts", "types/auth.ts",
                "hooks/useAuth.ts", "hooks/useApi.ts"
            ]
        },
        "vue": {
            "folder": "frontend",
            "files": [
                "package.json", "vite.config.ts", "tailwind.config.js", "postcss.config.js",
                "tsconfig.json", "index.html", "src/main.ts", "src/App.vue", "src/style.css",
                "src/components/ui/Button.vue", "src/components/ui/Input.vue", "src/components/ui/Modal.vue",
                "src/components/layout/Header.vue", "src/components/layout/Footer.vue", "src/components/layout/Sidebar.vue",
                "src/views/Home.vue", "src/views/Login.vue", "src/views/Dashboard.vue",
                "src/composables/useAuth.ts", "src/composables/useApi.ts", "src/composables/useLocalStorage.ts",
                "src/utils/api.ts", "src/utils/auth.ts", "src/utils/constants.ts",
                "src/types/index.ts", "src/types/api.ts", "src/types/auth.ts",
                "src/stores/auth.ts", "src/stores/index.ts", "src/router/index.ts"
            ]
        }
    }
    
    return structures.get(framework, structures["react"])

def get_backend_structure(framework: str, database: str) -> dict:
    """Get backend-specific file structure"""
    structures = {
        "fastapi": {
            "folder": "backend",
            "files": [
                "main.py", "requirements.txt", "models.py", "schemas.py", "auth.py", 
                "database.py", "config.py", ".env", "Dockerfile",
                "routers/auth.py", "routers/users.py", "routers/items.py",
                "utils/security.py", "utils/email.py", "utils/helpers.py",
                "tests/test_auth.py", "tests/test_users.py", "tests/conftest.py",
                "alembic.ini", "alembic/env.py", "alembic/versions/"
            ]
        },
        "django": {
            "folder": "backend",
            "files": [
                "manage.py", "requirements.txt", "settings.py", "urls.py", "wsgi.py", "asgi.py",
                "models.py", "views.py", "serializers.py", "admin.py", "apps.py", ".env", "Dockerfile",
                "authentication/models.py", "authentication/views.py", "authentication/serializers.py",
                "api/urls.py", "api/views.py", "api/serializers.py",
                "utils/permissions.py", "utils/helpers.py", "utils/email.py",
                "tests/test_models.py", "tests/test_views.py", "tests/test_auth.py"
            ]
        },
        "express": {
            "folder": "backend",
            "files": [
                "package.json", "server.js", "app.js", ".env", "Dockerfile",
                "routes/auth.js", "routes/users.js", "routes/items.js",
                "models/User.js", "models/Item.js", "models/index.js",
                "middleware/auth.js", "middleware/validation.js", "middleware/error.js",
                "controllers/authController.js", "controllers/userController.js", "controllers/itemController.js",
                "utils/jwt.js", "utils/email.js", "utils/helpers.js",
                "config/database.js", "config/config.js",
                "tests/auth.test.js", "tests/users.test.js", "tests/setup.js"
            ]
        }
    }
    
    return structures.get(framework, structures["fastapi"])

def get_integration_config(frontend: str, backend: str) -> dict:
    """Get integration configuration between frontend and backend"""
    combo_key = f"{frontend}_{backend}"
    return INTEGRATION_PATTERNS.get(combo_key, INTEGRATION_PATTERNS["react_fastapi"])

def detect_project_type(prompt: str) -> str:
    """Detect the type of project from the prompt"""
    prompt_lower = prompt.lower()
    
    if any(word in prompt_lower for word in ["todo", "task", "reminder"]):
        return "todo_app"
    elif any(word in prompt_lower for word in ["blog", "cms", "article", "post"]):
        return "blog_platform"
    elif any(word in prompt_lower for word in ["shop", "ecommerce", "store", "cart", "payment"]):
        return "ecommerce"
    elif any(word in prompt_lower for word in ["dashboard", "admin", "analytics"]):
        return "dashboard"
    elif any(word in prompt_lower for word in ["chat", "messaging", "social"]):
        return "social_app"
    else:
        return "web_app"
