{"generator": "bdist_wheel (0.26.0)", "summary": "Authorization for humans", "classifiers": ["Development Status :: 5 - Production/Stable", "Environment :: Web Environment", "Natural Language :: English", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: Apache Software License", "Operating System :: MacOS :: MacOS X", "Operating System :: Microsoft :: Windows", "Operating System :: POSIX", "Topic :: Software Development :: Libraries :: Python Modules", "Programming Language :: Python", "Programming Language :: Python :: Implementation :: CPython", "Programming Language :: Python :: Implementation :: PyPy", "Programming Language :: Python :: Implementation :: Jython", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3.5"], "extensions": {"python.details": {"project_urls": {"Home": "http://github.com/ourway/auth/"}, "contacts": [{"email": "<EMAIL>", "name": "Farsheed Ashouri", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}}, "python.exports": {"console_scripts": {"auth-server": "auth.cmd.server:main"}}, "python.commands": {"wrap_console": {"auth-server": "auth.cmd.server:main"}}}, "keywords": ["author<PERSON><PERSON>", "role", "auth", "groups", "membership", "ensure", "ldap"], "license": "Apache 2.0", "metadata_version": "2.0", "name": "auth", "run_requires": [{"requires": ["blinker", "eventlet", "falcon", "gunicorn", "mongoengine", "requests", "werkzeug"]}], "extras": [], "version": "0.5.3"}