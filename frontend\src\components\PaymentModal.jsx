import React, { useState } from 'react';
import { FiX, FiCreditCard, <PERSON><PERSON><PERSON>ck, FiLoader } from 'react-icons/fi';
import { createPaymentOrder, verifyPayment } from '../api';
import toast from 'react-hot-toast';

const PaymentModal = ({ isOpen, onClose, plan, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);

  const handlePayment = async () => {
    if (!plan || plan.price === 0) {
      toast.error('Invalid plan selected');
      return;
    }

    setLoading(true);

    try {
      // Create Razorpay order
      const orderResponse = await createPaymentOrder({ plan: plan.planId });
      
      // Initialize Razorpay
      const options = {
        key: orderResponse.key_id,
        amount: orderResponse.amount * 100, // Convert to paise
        currency: orderResponse.currency,
        name: 'VIKKI AI',
        description: `${plan.name} Subscription`,
        order_id: orderResponse.order_id,
        handler: async function (response) {
          setProcessing(true);
          try {
            // Verify payment
            const verifyResponse = await verifyPayment({
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              plan: plan.planId
            });

            if (verifyResponse.success) {
              toast.success('Payment successful! Plan upgraded.');
              onSuccess(plan.planId);
              onClose();
            } else {
              toast.error('Payment verification failed');
            }
          } catch (error) {
            console.error('Payment verification error:', error);
            toast.error('Payment verification failed');
          } finally {
            setProcessing(false);
          }
        },
        prefill: {
          name: 'User',
          email: '<EMAIL>'
        },
        theme: {
          color: '#06b6d4'
        },
        modal: {
          ondismiss: function() {
            setLoading(false);
          }
        }
      };

      const rzp = new window.Razorpay(options);
      rzp.open();

    } catch (error) {
      console.error('Payment error:', error);
      toast.error('Failed to initiate payment');
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 max-w-md w-full border border-gray-700/50 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-white">Upgrade to {plan?.name}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={loading || processing}
          >
            <FiX className="text-2xl" />
          </button>
        </div>

        {/* Plan Details */}
        <div className="bg-gradient-to-r from-cyan-900/20 to-blue-900/20 backdrop-blur-sm border border-cyan-500/20 rounded-xl p-6 mb-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-cyan-400 mb-2">
              ${plan?.price}
            </div>
            <div className="text-gray-300 mb-4">{plan?.period || 'per month'} • USD</div>
            
            <div className="space-y-2">
              {plan?.features?.map((feature, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-gray-300">
                  <FiCheck className="text-green-400 flex-shrink-0" />
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Payment Button */}
        <button
          onClick={handlePayment}
          disabled={loading || processing}
          className="w-full bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2"
        >
          {loading || processing ? (
            <>
              <FiLoader className="animate-spin" />
              {processing ? 'Processing...' : 'Initiating Payment...'}
            </>
          ) : (
            <>
              <FiCreditCard />
              Pay ${plan?.price} USD
            </>
          )}
        </button>

        {/* Security Note */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-400">
            🔒 Secure payment powered by Razorpay
          </p>
        </div>

        {/* Terms */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            By proceeding, you agree to our{' '}
            <a href="/terms" className="text-cyan-400 hover:underline">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="/privacy" className="text-cyan-400 hover:underline">
              Privacy Policy
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
