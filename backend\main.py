from fastapi import <PERSON>AP<PERSON>, HTTPException, Depends, Request, File, UploadFile, Form, APIRouter
from fastapi.security import OAuth2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse, StreamingResponse
from bson import ObjectId
import motor.motor_asyncio
import os
from codestral_integration import generate_single_file_code, generate_code, parse_project_files, generate_code_streaming
from models.models import PromptRequest, ChatRequest, ChatResponse, Project, TokenUsage, TokenInfo
from models.model_manager import ModelManager
from utils import save_project_files, zip_project_folder
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timedelta
from jose import JWTError, jwt
from typing import Optional, List, Dict, Literal
from pydantic import BaseModel
from dotenv import load_dotenv
from pathlib import Path
import shutil
import uuid
from fastapi.staticfiles import StaticFiles
from auth import get_current_user, User, create_access_token, get_current_active_user
import json
from sse_starlette.sse import EventSourceResponse
import secrets
from analysis.analyzer import ProjectAnalyzer

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="API Hub",
    description="A platform for generating and managing APIs",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins in development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/token")

# MongoDB configuration
MONGODB_URL = os.getenv("MONGODB_URL", "mongodb://localhost:27017/vikki-ai")
client = motor.motor_asyncio.AsyncIOMotorClient(MONGODB_URL)
db = client.apihub

# Pydantic models
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None

class UserInDB(User):
    hashed_password: str

class UserCreate(BaseModel):
    username: str
    email: str
    password: str
    full_name: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "username": "johndoe",
                "email": "<EMAIL>",
                "password": "secretpassword",
                "full_name": "John Doe"
            }
        }

class GenerateRequest(BaseModel):
    prompt: str

class Project(BaseModel):
    id: str
    name: str
    description: str
    created_at: datetime
    files: List[str]
    user_id: str
    file_contents: Optional[Dict[str, str]] = None

# Helper functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

async def get_user(username: str):
    user_dict = await db.users.find_one({"username": username})
    if user_dict:
        return UserInDB(**user_dict)

async def authenticate_user(username: str, password: str):
    user = await get_user(username)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = await get_user(username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Authentication endpoints
@app.post("/api/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = await authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.post("/api/register")
async def register_user(user: UserCreate):
    # Check if user already exists
    if await get_user(user.username):
        raise HTTPException(status_code=400, detail="Username already registered")
    
    # Create new user
    user_dict = user.dict()
    user_dict["hashed_password"] = get_password_hash(user_dict.pop("password"))
    user_dict["disabled"] = False  # Set default value for disabled field
    user_dict["full_name"] = user_dict.get("full_name")  # Ensure full_name is included
    
    # Insert user into database
    await db.users.insert_one(user_dict)
    
    return {"message": "User registered successfully"}

@app.get("/api/users/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    return current_user

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.post("/api/generate")
async def generate_code_endpoint(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    try:
        data = await request.json()
        prompt = data.get("prompt")
        model_type = data.get("model", "basic")  # Support model selection, default to 'basic'

        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")

        if not model_manager:
            raise HTTPException(status_code=500, detail="Model manager not initialized")

        # Generate project using ModelManager
        try:
            result = model_manager.generate_response(prompt, model_type)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Code generation failed: {str(e)}")

        if not result or "data" not in result or not result["data"].get("files"):
            raise HTTPException(status_code=500, detail="No files were generated for this prompt.")

        project_info = result["data"]["project"]
        files = result["data"]["files"]
        project_id = project_info["id"]

        return {
            "message": "Code generated successfully",
            "project_id": project_id,
            "files": [f["path"] for f in files],
            "download_url": f"/projects/{project_id}/download",
            "preview_url": f"/projects/{project_id}/preview"
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in generate_code_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/projects")
async def list_projects():
    projects = []
    async for proj in db.projects.find({}, {"prompt": 1}):
        projects.append({"id": str(proj["_id"]), "prompt": proj["prompt"]})
    return projects

@app.get("/api/projects/{project_id}")
async def get_project(project_id: str):
    proj = await db.projects.find_one({"_id": ObjectId(project_id)})
    if not proj:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"id": str(proj["_id"]), "prompt": proj["prompt"], "files": proj["files"]}

@app.get("/projects/{project_id}/download")
async def download_project(project_id: str, current_user: User = Depends(get_current_user)):
    try:
        project_dir = Path("generated_projects") / project_id
        if not project_dir.exists():
            raise HTTPException(status_code=404, detail="Project not found")
            
        # Read project info to verify ownership
        try:
            with open(project_dir / "project_info.json", "r") as f:
                project_info = json.loads(f.read())
        except:
            raise HTTPException(status_code=404, detail="Project info not found")
            
        if project_info.get("user_id") != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this project")
        
        # Create zip file
        zip_path = create_project_zip(project_id)
        if not zip_path or not os.path.exists(zip_path):
            raise HTTPException(status_code=500, detail="Failed to create zip file")
            
        return FileResponse(
            zip_path,
            media_type="application/zip",
            filename=f"project_{project_id}.zip"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in download_project: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def create_project_zip(project_id: str) -> str:
    """Create a zip file of the project directory"""
    try:
        project_dir = Path("generated_projects") / project_id
        zip_path = Path("generated_projects") / f"{project_id}.zip"
        
        # Remove existing zip if it exists
        if zip_path.exists():
            zip_path.unlink()
            
        # Create zip file
        shutil.make_archive(
            str(project_dir),  # Base name without extension
            'zip',            # Format
            project_dir       # Root directory
        )
        
        # Verify zip was created
        if not zip_path.exists():
            raise Exception("Failed to create zip file")
            
        return str(zip_path)
        
    except Exception as e:
        print(f"Error creating zip file: {str(e)}")
        raise

@app.post("/api/generate-files")
async def generate_project_files(req: PromptRequest):
    # example: split the project request into sub-prompts for backend, frontend, etc.
    prompts = [
        f"Generate backend code for: {req.prompt}",
        f"Generate frontend React Vite Tailwind CSS code for: {req.prompt}",
        f"Generate database models for: {req.prompt}"
    ]

    files = []
    for idx, p in enumerate(prompts):
        content = generate_single_file_code(p)
        files.append({
            "filename": f"file{idx+1}.py",  # adapt file extensions/names based on prompt
            "content": content
        })

    # Save to DB or return files directly
    return {"files": files}

# User related endpoints
@app.get("/users/stats")
async def get_user_stats(current_user: User = Depends(get_current_active_user)):
    user_stats = await db["users"].find_one(
        {"username": current_user.username},
        {"apiCalls": 1, "successRate": 1, "totalViews": 1, "projects": 1}
    )
    
    if not user_stats:
        return {
            "totalProjects": 0,
            "apiCalls": 0,
            "successRate": 100,
            "totalViews": 0
        }
    
    # Count total projects
    total_projects = len(user_stats.get("projects", []))
    
    return {
        "totalProjects": total_projects,
        "apiCalls": user_stats.get("apiCalls", 0),
        "successRate": user_stats.get("successRate", 100),
        "totalViews": user_stats.get("totalViews", 0)
    }

@app.get("/users/projects")
async def get_user_projects(current_user: User = Depends(get_current_user)):
    try:
        user_projects = []

        # Check if generated_projects directory exists
        projects_dir = Path("generated_projects")
        if not projects_dir.exists():
            print("Generated projects directory does not exist")
            return []

        # Get all project directories
        for project_dir in projects_dir.iterdir():
            if not project_dir.is_dir():
                continue

            # Check if this is a valid project directory
            project_info_file = project_dir / "project_info.json"
            if not project_info_file.exists():
                print(f"Skipping {project_dir.name}: no project_info.json")
                continue

            # Read project info
            try:
                with open(project_info_file, "r", encoding="utf-8") as f:
                    project_info = json.loads(f.read())

                # Only include projects for the current user
                if project_info.get("user_id") == current_user.username:
                    # Get list of files (with error handling)
                    try:
                        files = get_project_files(project_dir)
                        file_contents = read_project_files(project_dir)
                    except Exception as file_error:
                        print(f"Error reading files for project {project_dir.name}: {file_error}")
                        files = []
                        file_contents = {}

                    # Create project object
                    try:
                        created_at = datetime.fromisoformat(project_info.get("created_at", datetime.utcnow().isoformat()))
                    except ValueError:
                        created_at = datetime.utcnow()

                    project = Project(
                        id=project_dir.name,
                        name=project_info.get("name", f"Project {project_dir.name[:8]}"),
                        description=project_info.get("description", ""),
                        created_at=created_at,
                        files=files,
                        user_id=current_user.username,
                        file_contents=file_contents
                    )
                    user_projects.append(project)
            except json.JSONDecodeError as e:
                print(f"Error parsing project_info.json for {project_dir.name}: {e}")
                continue
            except Exception as e:
                print(f"Error processing project {project_dir.name}: {e}")
                continue

        return user_projects
    except Exception as e:
        print(f"Error in get_user_projects: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/projects/{project_id}/preview")
async def preview_project(project_id: str, current_user: User = Depends(get_current_user)):
    try:
        project_dir = Path("generated_projects") / project_id
        if not project_dir.exists():
            raise HTTPException(status_code=404, detail="Project not found")
            
        # Read project info
        try:
            with open(project_dir / "project_info.json", "r") as f:
                project_info = json.loads(f.read())
        except:
            raise HTTPException(status_code=404, detail="Project info not found")
            
        if project_info.get("user_id") != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this project")
            
        # Get files and their contents
        files = get_project_files(project_dir)
        file_contents = read_project_files(project_dir)
        
        return {
            "id": project_id,
            "name": project_info.get("name"),
            "description": project_info.get("description"),
            "created_at": project_info.get("created_at"),
            "files": files,
            "file_contents": file_contents,
            "download_url": f"/api/projects/{project_id}/download",
            "preview_url": f"/api/projects/{project_id}/preview"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def get_project_files(project_dir: Path) -> List[str]:
    """Get list of files in a project directory"""
    files = []
    for file_path in project_dir.rglob("*"):
        if file_path.is_file():
            files.append(str(file_path.relative_to(project_dir)))
    return files

def read_project_files(project_dir: Path) -> Dict[str, str]:
    """Read contents of all files in a project directory"""
    contents = {}
    for file_path in project_dir.rglob("*"):
        if file_path.is_file():
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    contents[str(file_path.relative_to(project_dir))] = f.read()
            except Exception as e:
                print(f"Error reading file {file_path}: {e}")
    return contents

# Initialize model manager
model_manager = None

@app.on_event("startup")
async def startup_event():
    global model_manager
    try:
        model_manager = ModelManager()
    except Exception as e:
        print(f"Error initializing model manager: {str(e)}")
        raise

@app.post("/api/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    try:
        if not model_manager:
            print("Error: Model manager not initialized")
            raise HTTPException(
                status_code=500,
                detail="AI models not properly initialized"
            )

        print(f"Processing chat request with model: {request.model}")
        print(f"Message: {request.message}")

        try:
            response = model_manager.generate_chat_response(
                request.message,
                request.model
            )
            print(f"Generated response: {response[:100]}...")  # Print first 100 chars
            return ChatResponse(response=response)
        except Exception as model_error:
            print(f"Error generating response: {str(model_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error generating response: {str(model_error)}"
            )
    except ValueError as e:
        print(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"Unexpected error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/stream-files")
async def stream_file_generation(request: Request, current_user: User = Depends(get_current_user)):
    """Stream file generation updates using Server-Sent Events"""
    try:
        body = await request.json()
        prompt = body.get("prompt")
        model_type = body.get("model", "basic")  # "basic" or "advanced"
        print(f"POST /stream-files: prompt={prompt}, model_type={model_type}, user_id={current_user.username}")

        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")

        if not model_manager:
            raise HTTPException(status_code=500, detail="Model manager not initialized")

        user_id = current_user.username

        async def event_generator():
            try:
                for update in generate_code_streaming(prompt, model_type, user_id=user_id):
                    # If project is complete, decrement tokens
                    if update.get("type") == "complete":
                        # Use a simple token usage metric: 1 token per 10 chars of prompt, min 1
                        tokens_used = max(1, len(prompt) // 10)
                        try:
                            await db.tokens.update_one(
                                {"user_id": user_id},
                                {"$inc": {"used_tokens": tokens_used}}
                            )
                        except Exception as e:
                            print(f"Failed to update token usage: {e}")
                    yield {
                        "event": "message",
                        "data": json.dumps(update)
                    }
            except Exception as e:
                print(f"Error in event_generator: {str(e)}")
                yield {
                    "event": "message",
                    "data": json.dumps({
                        "type": "error",
                        "data": {
                            "error": str(e)
                        }
                    })
                }

        return EventSourceResponse(event_generator())

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stream-files")
async def api_stream_files(request: Request, current_user: User = Depends(get_current_user)):
    # Proxy to the existing /stream-files endpoint with authentication
    try:
        return await stream_file_generation(request, current_user)
    except Exception as e:
        print(f"Error in api_stream_files: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Token management endpoints
@app.get("/tokens/info", response_model=TokenInfo)
async def get_token_info(current_user: User = Depends(get_current_active_user)):
    # Fetch user document to get the plan
    user_doc = await db.users.find_one({"username": current_user.username})
    plan = user_doc.get("subscription", {}).get("plan", "free") if user_doc else "free"
    total_tokens = PLAN_TOKEN_LIMITS.get(plan, 150)

    # Get user's token usage from database
    user_tokens = await db.tokens.find_one({"user_id": current_user.username})

    if not user_tokens:
        user_tokens = {
            "user_id": current_user.username,
            "total_tokens": total_tokens,
            "used_tokens": 0,
            "last_refresh": datetime.utcnow(),
            "usage_history": [],
            "plan": plan
        }
        await db.tokens.insert_one(user_tokens)
    else:
        # If plan changed, update total_tokens
        if user_tokens.get("total_tokens") != total_tokens:
            await db.tokens.update_one(
                {"user_id": current_user.username},
                {"$set": {"total_tokens": total_tokens}}
            )
            user_tokens["total_tokens"] = total_tokens

    remaining_tokens = user_tokens["total_tokens"] - user_tokens["used_tokens"]

    return {
        "total_tokens": user_tokens["total_tokens"],
        "used_tokens": user_tokens["used_tokens"],
        "remaining_tokens": remaining_tokens,
        "last_refresh": user_tokens["last_refresh"],
        "usage_history": user_tokens.get("usage_history", []),
        "plan": plan
    }

@app.post("/tokens/use")
async def use_tokens(
    usage: TokenUsage,
    current_user: User = Depends(get_current_active_user)
):
    # Get current token info
    user_tokens = await db.tokens.find_one({"user_id": current_user.username})
    
    if not user_tokens:
        raise HTTPException(status_code=400, detail="No token allocation found")
    
    # Check if user has enough tokens
    remaining_tokens = user_tokens["total_tokens"] - user_tokens["used_tokens"]
    if remaining_tokens < usage.tokens_used:
        raise HTTPException(status_code=400, detail="Insufficient tokens")
    
    # Update token usage
    usage.user_id = current_user.username
    usage.timestamp = datetime.utcnow()
    
    await db.tokens.update_one(
        {"user_id": current_user.username},
        {
            "$inc": {"used_tokens": usage.tokens_used},
            "$push": {"usage_history": usage.dict()}
        }
    )
    
    return {"message": "Tokens used successfully", "remaining_tokens": remaining_tokens - usage.tokens_used}

@app.post("/tokens/refresh")
async def refresh_tokens(current_user: User = Depends(get_current_active_user)):
    # Get current token info
    user_tokens = await db.tokens.find_one({"user_id": current_user.username})
    
    if not user_tokens:
        raise HTTPException(status_code=400, detail="No token allocation found")
    
    # Check if enough time has passed since last refresh (e.g., monthly)
    last_refresh = user_tokens["last_refresh"]
    if datetime.utcnow() - last_refresh < timedelta(days=30):
        raise HTTPException(
            status_code=400,
            detail="Tokens can only be refreshed once per month"
        )
    
    # Refresh tokens
    await db.tokens.update_one(
        {"user_id": current_user.username},
        {
            "$set": {
                "used_tokens": 0,
                "last_refresh": datetime.utcnow()
            }
        }
    )
    
    return {"message": "Tokens refreshed successfully", "total_tokens": user_tokens["total_tokens"]}

# --- API-prefixed Token management endpoints for frontend compatibility ---
tokens_router = APIRouter(prefix="/api/tokens", tags=["tokens"])

# Plan to token mapping
PLAN_TOKEN_LIMITS = {
    "free": 150,
    "pro": 500,
    "enterprise": 1000
}

@tokens_router.get("/info")
async def api_get_token_info(current_user: User = Depends(get_current_active_user)):
    # Fetch user document to get the plan
    user_doc = await db.users.find_one({"username": current_user.username})
    plan = user_doc.get("subscription", {}).get("plan", "free") if user_doc else "free"
    total_tokens = PLAN_TOKEN_LIMITS.get(plan, 150)

    # Get user's token usage from database
    user_tokens = await db.tokens.find_one({"user_id": current_user.username})

    if not user_tokens:
        user_tokens = {
            "user_id": current_user.username,
            "total_tokens": total_tokens,
            "used_tokens": 0,
            "last_refresh": datetime.utcnow(),
            "usage_history": [],
            "plan": plan
        }
        await db.tokens.insert_one(user_tokens)
    else:
        # If plan changed, update total_tokens
        if user_tokens.get("total_tokens") != total_tokens:
            await db.tokens.update_one(
                {"user_id": current_user.username},
                {"$set": {"total_tokens": total_tokens}}
            )
            user_tokens["total_tokens"] = total_tokens

    remaining_tokens = user_tokens["total_tokens"] - user_tokens["used_tokens"]

    return {
        "total_tokens": user_tokens["total_tokens"],
        "used_tokens": user_tokens["used_tokens"],
        "remaining_tokens": remaining_tokens,
        "last_refresh": user_tokens["last_refresh"],
        "usage_history": user_tokens.get("usage_history", []),
        "plan": plan
    }

@tokens_router.post("/use")
async def api_use_tokens(
    usage: TokenUsage,
    current_user: User = Depends(get_current_active_user)
):
    user_tokens = await db.tokens.find_one({"user_id": current_user.username})
    if not user_tokens:
        raise HTTPException(status_code=400, detail="No token allocation found")
    remaining_tokens = user_tokens["total_tokens"] - user_tokens["used_tokens"]
    if remaining_tokens < usage.tokens_used:
        raise HTTPException(status_code=400, detail="Insufficient tokens")
    usage.user_id = current_user.username
    usage.timestamp = datetime.utcnow()
    await db.tokens.update_one(
        {"user_id": current_user.username},
        {
            "$inc": {"used_tokens": usage.tokens_used},
            "$push": {"usage_history": usage.dict()}
        }
    )
    return {"message": "Tokens used successfully", "remaining_tokens": remaining_tokens - usage.tokens_used}

@tokens_router.post("/refresh")
async def api_refresh_tokens(current_user: User = Depends(get_current_active_user)):
    user_tokens = await db.tokens.find_one({"user_id": current_user.username})
    if not user_tokens:
        raise HTTPException(status_code=400, detail="No token allocation found")
    last_refresh = user_tokens["last_refresh"]
    if datetime.utcnow() - last_refresh < timedelta(days=30):
        raise HTTPException(
            status_code=400,
            detail="Tokens can only be refreshed once per month"
        )
    await db.tokens.update_one(
        {"user_id": current_user.username},
        {
            "$set": {
                "used_tokens": 0,
                "last_refresh": datetime.utcnow()
            }
        }
    )
    return {"message": "Tokens refreshed successfully", "total_tokens": user_tokens["total_tokens"]}

# Register the router
app.include_router(tokens_router)

@app.get("/api/users/api-key")
async def get_api_key(current_user: User = Depends(get_current_active_user)):
    user_doc = await db.users.find_one({"username": current_user.username})
    if not user_doc:
        raise HTTPException(status_code=404, detail="User not found")
    api_key = user_doc.get("api_key")
    if not api_key:
        # Generate a new API key
        api_key = secrets.token_hex(32)
        await db.users.update_one({"username": current_user.username}, {"$set": {"api_key": api_key}})
    return {"api_key": api_key}

@app.post("/api/users/api-key/regenerate")
async def regenerate_api_key(current_user: User = Depends(get_current_active_user)):
    api_key = secrets.token_hex(32)
    await db.users.update_one({"username": current_user.username}, {"$set": {"api_key": api_key}})
    return {"api_key": api_key}

# Initialize the analyzer
project_analyzer = ProjectAnalyzer()

@app.post("/api/analyze")
async def analyze_project(files: list[UploadFile] = File(...)):
    temp_dir = Path("temp_uploads")
    temp_dir.mkdir(exist_ok=True)
    saved_files = []
    try:
        for file in files:
            file_path = temp_dir / file.filename
            with file_path.open("wb") as buffer:
                buffer.write(await file.read())
            with file_path.open("r", encoding="utf-8") as f:
                content = f.read()
            saved_files.append({"path": str(file_path), "content": content})
        # Analyze the project using ProjectAnalyzer (which can use mistral/codestral as needed)
        analysis_results = project_analyzer.analyze_project(saved_files)
        shutil.rmtree(temp_dir)
        return analysis_results
    except Exception as e:
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        raise HTTPException(status_code=500, detail=str(e))

