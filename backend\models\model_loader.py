from llama_cpp import <PERSON>lama
import os
from typing import Dict, Any
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

MODEL_MAP = {
    "vikki": {
        "path": "models/mistral-7b-instruct-v0.2.Q5_K_M.gguf",
        "instance": None
    },
    "vikki_advanced": {
        "path": "models/Codestral-22B-v0.1-Q4_K_M.gguf",
        "instance": None
    }
}

class ModelLoader:
    def __init__(self):
        self.models = {}
        self.tokenizers = {}
        self.load_models()

    def load_models(self):
        """Load all available models"""
        model_paths = {
            "vikki": "mistralai/Mistral-7B-Instruct-v0.2",
            "vikki_advanced": "codestral-22b-v0.1"
        }

        for model_name, model_path in model_paths.items():
            try:
                self.tokenizers[model_name] = AutoTokenizer.from_pretrained(model_path)
                self.models[model_name] = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    torch_dtype=torch.float16,
                    device_map="auto"
                )
                print(f"Successfully loaded {model_name} model")
            except Exception as e:
                print(f"Error loading {model_name} model: {str(e)}")

    def get_model(self, model_name: str):
        """Get a specific model by name"""
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found")
        return self.models[model_name]

    def get_chat_response(self, message: str, model_name: str = "vikki") -> str:
        """Generate a chat response using the specified model"""
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found")

        model = self.models[model_name]
        tokenizer = self.tokenizers[model_name]

        # Format the prompt for chat
        prompt = f"<s>[INST] {message} [/INST]"
        
        # Generate response
        inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
        outputs = model.generate(
            **inputs,
            max_new_tokens=512,
            temperature=0.7,
            top_p=0.95,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
        
        # Decode and clean up response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = response.replace(prompt, "").strip()
        
        return response

    def generate_code(self, prompt: str, model_name: str = "vikki") -> Dict[str, Any]:
        """Generate code using the specified model"""
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found")

        model = self.models[model_name]
        tokenizer = self.tokenizers[model_name]

        # Format the prompt for code generation
        prompt = f"<s>[INST] Generate code for: {prompt} [/INST]"
        
        # Generate response
        inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
        outputs = model.generate(
            **inputs,
            max_new_tokens=1024,
            temperature=0.7,
            top_p=0.95,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
        
        # Decode and clean up response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = response.replace(prompt, "").strip()
        
        # Parse the response to extract files
        # This is a simple implementation - you might want to make it more robust
        files = []
        current_file = None
        current_content = []
        
        for line in response.split('\n'):
            if line.startswith('```'):
                if current_file:
                    files.append({
                        'name': current_file,
                        'content': '\n'.join(current_content)
                    })
                    current_file = None
                    current_content = []
                else:
                    # Extract filename from the code block marker
                    current_file = line.strip('```').strip()
            elif current_file:
                current_content.append(line)
        
        return {
            'project': {
                'id': 'generated_' + str(hash(prompt)),
                'files': files
            }
        }

def get_model_instance(model_key: str) -> Llama:
    """
    Get or create a model instance for the specified model key.
    
    Args:
        model_key (str): The key of the model to load ("vikki" or "vikki_advanced")
        
    Returns:
        Llama: The loaded model instance
        
    Raises:
        ValueError: If the model key is not found
    """
    model_info = MODEL_MAP.get(model_key)
    if model_info is None:
        raise ValueError(f"Model '{model_key}' not found")

    # Lazy load model if not already loaded
    if model_info["instance"] is None:
        model_path = os.path.join(os.path.dirname(__file__), model_info["path"])
        if not os.path.exists(model_path):
            raise ValueError(f"Model file not found at {model_path}")
            
        model_info["instance"] = Llama(
            model_path=model_path,
            n_ctx=1500,
            n_threads=8
        )

    return model_info["instance"]

def unload_model(model_key: str) -> None:
    """
    Unload a model instance to free up memory.
    
    Args:
        model_key (str): The key of the model to unload
    """
    model_info = MODEL_MAP.get(model_key)
    if model_info and model_info["instance"] is not None:
        model_info["instance"] = None

def unload_all_models() -> None:
    """
    Unload all model instances to free up memory.
    """
    for model_key in MODEL_MAP:
        unload_model(model_key) 