#!/usr/bin/env python3
"""
Test script to verify token refresh endpoint
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_token_refresh():
    """Test token refresh functionality"""
    print("=== Testing Token Refresh ===")
    
    # First, login to get a token
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/token", 
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        if response.status_code == 200:
            token_data = response.json()
            token = token_data["access_token"]
            print("✓ Login successful")
        else:
            print(f"✗ Login failed: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"✗ Login request failed: {e}")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test token info first
    try:
        response = requests.get(f"{BASE_URL}/api/tokens/info", headers=headers)
        if response.status_code == 200:
            token_info = response.json()
            print(f"✓ Token info: {token_info['remaining_tokens']} tokens remaining")
        else:
            print(f"✗ Token info failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ Token info request failed: {e}")
    
    # Test token refresh
    try:
        response = requests.post(f"{BASE_URL}/api/tokens/refresh", headers=headers)
        if response.status_code == 200:
            refresh_data = response.json()
            print(f"✓ Token refresh successful: {refresh_data['message']}")
        elif response.status_code == 400:
            print(f"⚠ Token refresh limited: {response.json().get('detail', 'Unknown error')}")
        else:
            print(f"✗ Token refresh failed: {response.status_code} - {response.text}")
            print(f"Response content: {response.content}")
    except Exception as e:
        print(f"✗ Token refresh request failed: {e}")

if __name__ == "__main__":
    print("🧪 Testing Token Refresh Endpoint")
    print("Make sure the backend is running on http://localhost:8000")
    print()
    
    test_token_refresh()
    
    print("\n=== Test Complete ===")
