import React, { useState } from 'react';
import { FiChevronDown, FiChevronUp, FiHelpCircle, FiCode, FiKey, FiZap } from 'react-icons/fi';
import PageContainer from '../components/layout/PageContainer';

const FAQ = () => {
  const [openItems, setOpenItems] = useState({});

  const toggleItem = (index) => {
    setOpenItems(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const faqCategories = [
    {
      title: "Getting Started",
      icon: <FiHelpCircle className="text-cyan-400" />,
      faqs: [
        {
          question: "What is VIKKI AI?",
          answer: "VIKKI AI is an advanced AI-powered code generation platform that helps developers create complete full-stack applications from simple text prompts. It supports multiple frameworks including React, Next.js, FastAPI, Django, and more."
        },
        {
          question: "How do I get started with VIKKI AI?",
          answer: "Simply sign up for a free account, and you'll get 150 tokens to start generating projects. You can use the Generate page to create new projects or the Chat feature to ask for specific code modifications."
        },
        {
          question: "What programming languages and frameworks are supported?",
          answer: "VIKKI AI supports a wide range of technologies including React, Next.js, Vue.js, Angular, FastAPI, Django, Flask, Express.js, Node.js, Python, JavaScript, TypeScript, and many more. The AI automatically detects your preferences from your prompts."
        },
        {
          question: "Is there a free plan available?",
          answer: "Yes! We offer a free plan with 150 tokens that refresh every 24 hours. This allows you to generate 3 full projects or 15 chat interactions daily. For more usage, you can upgrade to our Pro or Enterprise plans."
        }
      ]
    },
    {
      title: "Using VIKKI AI",
      icon: <FiCode className="text-blue-400" />,
      faqs: [
        {
          question: "How do I generate a full-stack application?",
          answer: "Go to the Generate page and describe your project in natural language. For example: 'Create a React todo app with FastAPI backend' or 'Build a Next.js ecommerce store with user authentication'. VIKKI AI will generate all necessary files, including frontend, backend, database models, and configuration files."
        },
        {
          question: "Can I modify existing projects?",
          answer: "Yes! Use the Chat feature to modify existing projects. You can ask for specific changes like 'Add a dark mode toggle' or 'Create a new user profile component'. The AI will understand the context and provide the necessary code modifications."
        },
        {
          question: "What file formats are generated?",
          answer: "VIKKI AI generates complete project structures with all necessary files including source code (.js, .jsx, .ts, .tsx, .py), configuration files (package.json, requirements.txt), styling files (.css, .scss), documentation (README.md), and deployment files (Dockerfile, docker-compose.yml)."
        },
        {
          question: "How do I download my generated projects?",
          answer: "After generation, you can download your complete project as a ZIP file from the dashboard. The package includes all source files, dependencies, setup instructions, and deployment guides."
        },
        {
          question: "Can I preview my applications before downloading?",
          answer: "Yes! VIKKI AI includes a live preview feature that automatically starts your application in a containerized environment, allowing you to see how it looks and functions before downloading."
        }
      ]
    },
    {
      title: "API & Integration",
      icon: <FiKey className="text-green-400" />,
      faqs: [
        {
          question: "How do I get an API key?",
          answer: "After logging in, go to your Profile page and navigate to the API section. Click 'Generate API Key' to create your unique key. You can regenerate it anytime if needed for security purposes."
        },
        {
          question: "How do I use the VIKKI AI API in my IDE?",
          answer: "You can integrate VIKKI AI into your favorite IDE using our REST API. Send POST requests to our endpoints with your API key in the headers. Check our API Documentation page for detailed examples and code snippets for VS Code, IntelliJ, and other popular IDEs."
        },
        {
          question: "What are the API rate limits?",
          answer: "API rate limits depend on your plan: Free (150 tokens/day), Pro (500 tokens/day), Enterprise (1000 tokens/day). Each API call consumes tokens based on the operation: project generation (50 tokens), chat (10 tokens), code analysis (5 tokens)."
        },
        {
          question: "Can I use the API for commercial projects?",
          answer: "Yes! All our plans, including the free tier, allow commercial use of generated code. The code you generate belongs to you and can be used in any commercial or personal projects without restrictions."
        },
        {
          question: "Is there a webhook system for notifications?",
          answer: "Yes, our Pro and Enterprise plans include webhook support. You can configure webhooks to receive notifications when projects are generated, when API limits are reached, or for other important events."
        }
      ]
    },
    {
      title: "Tokens & Pricing",
      icon: <FiZap className="text-yellow-400" />,
      faqs: [
        {
          question: "How does the token system work?",
          answer: "Tokens are used to track usage across our platform. Different actions consume different amounts: Project Generation (50 tokens), Chat interactions (10 tokens), Code analysis (5 tokens), File modifications (15 tokens). Tokens refresh every 24 hours based on your plan."
        },
        {
          question: "What happens when I run out of tokens?",
          answer: "When you run out of tokens, you'll see an upgrade prompt with options to either wait for the next refresh (24 hours) or upgrade to a higher plan for more tokens and additional features."
        },
        {
          question: "Can I purchase additional tokens?",
          answer: "Currently, tokens are plan-based and refresh daily. You can upgrade your plan for more daily tokens and additional features. We're working on a pay-per-use option for occasional high-volume needs."
        },
        {
          question: "Do unused tokens carry over?",
          answer: "No, tokens refresh to your plan's limit every 24 hours. Unused tokens don't accumulate, encouraging regular use of the platform while maintaining fair usage across all users."
        },
        {
          question: "What's included in each plan?",
          answer: "Free: 150 tokens/day, basic features. Pro: 500 tokens/day, priority support, advanced features, webhooks. Enterprise: 1000 tokens/day, dedicated support, custom integrations, SLA guarantees. All plans include commercial usage rights."
        }
      ]
    }
  ];

  return (
    <PageContainer>
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] text-white pt-20">
        {/* Hero Section */}
        <div className="relative py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
              Everything you need to know about VIKKI AI and how to use it effectively
            </p>
          </div>
        </div>

        {/* FAQ Categories */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            {faqCategories.map((category, categoryIndex) => (
              <div key={categoryIndex} className="mb-12">
                {/* Category Header */}
                <div className="flex items-center gap-4 mb-8">
                  <div className="text-3xl">
                    {category.icon}
                  </div>
                  <h2 className="text-3xl md:text-4xl font-bold text-white">
                    {category.title}
                  </h2>
                </div>

                {/* FAQ Items */}
                <div className="space-y-4">
                  {category.faqs.map((faq, faqIndex) => {
                    const itemKey = `${categoryIndex}-${faqIndex}`;
                    const isOpen = openItems[itemKey];

                    return (
                      <div
                        key={faqIndex}
                        className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl overflow-hidden hover:border-cyan-500/30 transition-all duration-300"
                      >
                        <button
                          onClick={() => toggleItem(itemKey)}
                          className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-800/20 transition-colors duration-200"
                        >
                          <h3 className="text-lg md:text-xl font-semibold text-white pr-4">
                            {faq.question}
                          </h3>
                          <div className="flex-shrink-0">
                            {isOpen ? (
                              <FiChevronUp className="text-2xl text-cyan-400" />
                            ) : (
                              <FiChevronDown className="text-2xl text-gray-400" />
                            )}
                          </div>
                        </button>

                        {isOpen && (
                          <div className="px-8 pb-6">
                            <div className="border-t border-gray-700/30 pt-6">
                              <p className="text-gray-300 leading-relaxed text-lg">
                                {faq.answer}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-cyan-900/20 to-blue-900/20 backdrop-blur-sm border border-cyan-500/20 rounded-3xl p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Still have questions?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Can't find the answer you're looking for? Our support team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105">
                  Contact Support
                </button>
                <button className="border border-gray-600 hover:border-cyan-400 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:bg-cyan-400/10">
                  Join Discord
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default FAQ;
