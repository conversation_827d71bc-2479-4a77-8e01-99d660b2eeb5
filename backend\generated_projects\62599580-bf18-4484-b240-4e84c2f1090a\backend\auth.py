import jwt
import datetime
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session
from models import User, Base
from database import get_db

class Authenticator:
    def __init__(self):
        self.secret_key = "your_secret_key"

    def authenticate_user(self, fake_db: Session, username: str, password: str):
        user = fake_db.query(User).filter(User.username == username).first()

        if not user or user.password != password:
            return False

        return user

    def create_access_token(self, data: dict, expires_delta: timedelta):
        to_encode = data.copy()
        to_encode.update({"exp": int(time.time()) + expires_delta.total_seconds()})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm="HS256")
        return encoded_jwt
