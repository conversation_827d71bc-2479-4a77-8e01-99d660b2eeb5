{"id": "c7f6f754-89d5-488b-b375-88ea81ea0a90", "name": "Project c7f6f754", "description": "build me the soap store a complete online application in react fastapi, tailwindcss and mongodb data...", "created_at": "2025-06-03T16:02:34.894320", "user_id": "abhay", "files": ["frontend/package.json", "frontend/src/App.jsx", "frontend/src/index.css", "backend/requirements.txt", "backend/pyproject.toml", "backend/main.py", "backend/models/item.py", "backend/schemas/item.py", "backend/database/database.py"]}