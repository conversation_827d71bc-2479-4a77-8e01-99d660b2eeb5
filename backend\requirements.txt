# Core FastAPI and Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
aiofiles==23.2.1
sse-starlette==1.8.2

# Database and ODM
motor==3.3.2
pymongo==4.6.0

# Data Models and Validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication and Security
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
bcrypt==4.1.2
python-dotenv==1.0.0

# AI and Machine Learning
llama-cpp-python==0.2.20
torch==2.1.0
transformers==4.35.0
accelerate==0.24.0
sentencepiece==0.1.99

# Image Generation and Processing
Pillow==10.1.0
aiohttp==3.9.1
requests==2.31.0

# Docker and Container Management
docker==6.1.3

# File Processing and Compression
zipfile36==0.1.3

# Code Analysis and Parsing
javalang==0.13.0
esprima==4.0.1

# Development and Debugging
python-multipart==0.0.6

# Additional Utilities
pathlib2==2.3.7
asyncio-mqtt==0.16.1

# Optional: Stable Diffusion API (if using Stability AI)
# stability-sdk==0.8.4

# Optional: Local Stable Diffusion (if using Automatic1111)
# gradio==4.7.1

# Testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
