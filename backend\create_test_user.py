#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create a test user for debugging authentication issues
"""

import asyncio
import motor.motor_asyncio
from passlib.context import CryptContext
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# MongoDB configuration
MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://localhost:27017/vikki-ai")
client = motor.motor_asyncio.AsyncIOMotorClient(MONGODB_URI)
db = client["vikki-ai"]

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def create_test_user():
    """Create a test user for authentication testing"""
    
    # Test user data
    test_user = {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "hashed_password": pwd_context.hash("testpassword123"),
        "disabled": False
    }
    
    try:
        # Check if user already exists
        existing_user = await db.users.find_one({"username": test_user["username"]})
        if existing_user:
            print(f"User '{test_user['username']}' already exists!")
            return
        
        # Create the user
        result = await db.users.insert_one(test_user)
        print(f"Test user created successfully!")
        print(f"Username: {test_user['username']}")
        print(f"Password: testpassword123")
        print(f"Email: {test_user['email']}")
        print(f"User ID: {result.inserted_id}")
        
        # Create initial token allocation
        token_allocation = {
            "user_id": test_user["username"],
            "total_tokens": 150,
            "used_tokens": 0,
            "last_refresh": None,
            "usage_history": [],
            "plan": "free"
        }
        
        await db.tokens.insert_one(token_allocation)
        print("Token allocation created for test user")
        
    except Exception as e:
        print(f"Error creating test user: {str(e)}")
    finally:
        client.close()

async def list_users():
    """List all users in the database"""
    try:
        users = []
        async for user in db.users.find({}, {"hashed_password": 0}):  # Exclude password hash
            users.append(user)
        
        print(f"\nFound {len(users)} users in database:")
        for user in users:
            print(f"- Username: {user['username']}, Email: {user.get('email', 'N/A')}")
            
    except Exception as e:
        print(f"Error listing users: {str(e)}")
    finally:
        client.close()

async def test_password_verification():
    """Test password verification for the test user"""
    try:
        user = await db.users.find_one({"username": "testuser"})
        if not user:
            print("Test user not found!")
            return
        
        # Test correct password
        is_valid = pwd_context.verify("testpassword123", user["hashed_password"])
        print(f"Password verification test: {'PASSED' if is_valid else 'FAILED'}")
        
        # Test incorrect password
        is_invalid = pwd_context.verify("wrongpassword", user["hashed_password"])
        print(f"Wrong password test: {'PASSED' if not is_invalid else 'FAILED'}")
        
    except Exception as e:
        print(f"Error testing password verification: {str(e)}")
    finally:
        client.close()

async def main():
    """Main function"""
    print("VIKKI AI Authentication Test Utility")
    print("=" * 40)
    
    while True:
        print("\nOptions:")
        print("1. Create test user")
        print("2. List all users")
        print("3. Test password verification")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            await create_test_user()
        elif choice == "2":
            await list_users()
        elif choice == "3":
            await test_password_verification()
        elif choice == "4":
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    asyncio.run(main())
