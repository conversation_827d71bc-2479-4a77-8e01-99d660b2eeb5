import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiGithub, FiLinkedin } from 'react-icons/fi'

export default function Contact() {

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#181a20] via-[#232526] to-[#23243a] text-white font-sans pt-20 py-12" style={{ fontFamily: 'Inter, Space Grotesk, sans-serif' }}>
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold bg-gradient-to-r from-cyan-400 to-blue-600 text-transparent bg-clip-text drop-shadow-glow tracking-wider mb-6">Contact Us</h1>
          <p className="text-xl text-cyan-200 font-medium leading-relaxed">
            Have questions about VIKKI AI? We're here to help you build amazing applications with our AI-powered development platform.
          </p>
        </div>

        <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-3xl shadow-2xl p-12 backdrop-blur-xl">
          <div className="text-center space-y-8">
            {/* Main Content */}
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-cyan-100 mb-6">Get in Touch</h2>
              <p className="text-lg text-cyan-200/90 leading-relaxed max-w-3xl mx-auto">
                Whether you need help with our AI code generation tools, have questions about our platform,
                or want to share feedback, we'd love to hear from you. Our team is dedicated to helping
                developers create better applications faster with VIKKI AI.
              </p>
            </div>

            {/* Email Section */}
            <div className="bg-gradient-to-r from-cyan-900/20 to-blue-900/20 border border-cyan-900/30 rounded-2xl p-8 max-w-md mx-auto">
              <div className="flex items-center justify-center gap-4 mb-4">
                <FiMail className="h-8 w-8 text-cyan-400 animate-pulse drop-shadow-glow" />
                <h3 className="text-xl font-bold text-cyan-100">Email Us</h3>
              </div>
              <a
                href="mailto:<EMAIL>"
                className="text-2xl font-bold text-cyan-400 hover:text-cyan-300 transition-colors duration-200 block"
              >
                <EMAIL>
              </a>
              <p className="text-cyan-200/80 mt-3">
                We typically respond within 24 hours
              </p>
            </div>

            {/* Follow Us Section */}
            <div className="space-y-6">
              <h3 className="text-xl font-bold text-cyan-100">Follow Us</h3>
              <div className="flex justify-center space-x-8">
                <a
                  href="https://twitter.com/vikki_ai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-cyan-400 hover:text-cyan-300 transition-colors duration-200 transform hover:scale-110"
                >
                  <FiTwitter className="w-8 h-8" />
                </a>
                <a
                  href="https://github.com/vikki-ai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-cyan-400 hover:text-cyan-300 transition-colors duration-200 transform hover:scale-110"
                >
                  <FiGithub className="w-8 h-8" />
                </a>
                <a
                  href="https://linkedin.com/company/vikki-ai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-cyan-400 hover:text-cyan-300 transition-colors duration-200 transform hover:scale-110"
                >
                  <FiLinkedin className="w-8 h-8" />
                </a>
              </div>
            </div>

            {/* Additional Info */}
            <div className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border border-green-900/30 rounded-xl p-6 max-w-2xl mx-auto">
              <h4 className="text-lg font-bold text-green-200 mb-3">What can we help you with?</h4>
              <ul className="text-green-100/90 text-left space-y-2">
                <li>• Technical support and troubleshooting</li>
                <li>• Questions about AI code generation features</li>
                <li>• Billing and subscription inquiries</li>
                <li>• Feature requests and feedback</li>
                <li>• Partnership and collaboration opportunities</li>
              </ul>
            </div>
          </div>
        </div>

        <style>{`.drop-shadow-glow { filter: drop-shadow(0 0 6px #00fff7cc); }`}</style>
      </div>
    </div>
  )
} 