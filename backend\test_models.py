#!/usr/bin/env python3
"""
Test script to check if models can be loaded properly
"""
import sys
from pathlib import Path

def test_model_paths():
    """Test if model files exist"""
    print("Testing model file paths...")
    
    models_dir = Path("models")
    print(f"Models directory exists: {models_dir.exists()}")
    
    if models_dir.exists():
        print("Files in models directory:")
        for file in models_dir.iterdir():
            print(f"  - {file.name} ({file.stat().st_size / (1024*1024*1024):.2f} GB)")
    
    mistral_path = models_dir / "mistral-7b-instruct-v0.2.Q5_K_M.gguf"
    codestral_path = models_dir / "Codestral-22B-v0.1-Q4_K_M.gguf"
    
    print(f"Mistral model exists: {mistral_path.exists()}")
    print(f"Codestral model exists: {codestral_path.exists()}")
    
    return mistral_path.exists()

def test_imports():
    """Test if required modules can be imported"""
    print("\nTesting imports...")
    
    try:
        from llama_cpp import Llama
        print("✓ llama_cpp imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import llama_cpp: {e}")
        return False
    
    try:
        from models.model_manager import ModelManager
        print("✓ ModelManager imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ModelManager: {e}")
        return False
    
    try:
        from codestral_integration import generate_code_streaming
        print("✓ codestral_integration imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import codestral_integration: {e}")
        return False
    
    return True

def test_model_loading():
    """Test if models can be loaded"""
    print("\nTesting model loading...")
    
    try:
        from models.model_manager import ModelManager
        print("Creating ModelManager instance...")
        mm = ModelManager()
        print("✓ ModelManager created successfully")
        
        # Test getting mistral model
        try:
            model = mm.get_model("basic")
            print("✓ Basic model (Mistral) loaded successfully")
        except Exception as e:
            print(f"✗ Failed to load basic model: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to create ModelManager: {e}")
        return False

if __name__ == "__main__":
    print("=== Model Testing Script ===")
    
    # Test 1: Check file paths
    paths_ok = test_model_paths()
    
    # Test 2: Check imports
    imports_ok = test_imports()
    
    # Test 3: Check model loading (only if paths and imports are OK)
    if paths_ok and imports_ok:
        loading_ok = test_model_loading()
    else:
        loading_ok = False
        print("\nSkipping model loading test due to previous failures")
    
    print("\n=== Test Results ===")
    print(f"Model paths: {'✓' if paths_ok else '✗'}")
    print(f"Imports: {'✓' if imports_ok else '✗'}")
    print(f"Model loading: {'✓' if loading_ok else '✗'}")
    
    if paths_ok and imports_ok and loading_ok:
        print("\n🎉 All tests passed! Models should work correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        sys.exit(1)
