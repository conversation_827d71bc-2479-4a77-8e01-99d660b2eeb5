import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Suspense, lazy } from 'react'
import { Toaster } from 'react-hot-toast'
import Navbar from './components/layout/Navbar'
import Footer from './components/layout/Footer'
import { ThemeProvider } from './context/ThemeContext'
import { AuthProvider } from './hooks/useAuth'
import ProtectedRoute from './components/auth/ProtectedRoute'
import ErrorBoundary from './components/ErrorBoundary'
import AppRoutes from './routes'

// Loading component
const LoadingSpinner = () => (
  <div className="min-h-screen bg-[#1e1e1e] flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
      <p className="mt-4 text-gray-400">Loading...</p>
    </div>
  </div>
)

// Lazy load pages
const Home = lazy(() => import('./pages/Home'))
const Login = lazy(() => import('./pages/auth/Login'))
const Register = lazy(() => import('./pages/auth/Register'))
const ResetPassword = lazy(() => import('./pages/auth/ResetPassword'))
const UserProfile = lazy(() => import('./pages/profile/UserProfile'))
const Forum = lazy(() => import('./pages/Forum'))
const Contact = lazy(() => import('./pages/Contact'))
const Generate = lazy(() => import('./pages/Generate'))
const Analyze = lazy(() => import('./pages/Analyze'))
const ApiGeneration = lazy(() => import('./pages/api/ApiGeneration'))
const ApiDocs = lazy(() => import('./pages/api/ApiDocs'))
const About = lazy(() => import('./pages/About'))
const Pricing = lazy(() => import('./pages/Pricing'))
const Dashboard = lazy(() => import('./pages/Dashboard'))
const ProjectAnalysis = lazy(() => import('./pages/ProjectAnalysis'))
const CreateTopic = lazy(() => import('./pages/CreateTopic'))
const TopicDetail = lazy(() => import('./pages/TopicDetail'))
const FAQ = lazy(() => import('./pages/FAQ'))
const Privacy = lazy(() => import('./pages/Privacy'))
const Terms = lazy(() => import('./pages/Terms'))
const Cookies = lazy(() => import('./pages/Cookies'))
const Status = lazy(() => import('./pages/Status'))

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <AuthProvider>
          <div className="min-h-screen bg-[#1e1e1e] text-white flex flex-col">
            <Navbar />
            <main className="flex-grow">
              <Suspense fallback={<LoadingSpinner />}>
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/reset-password" element={<ResetPassword />} />
                  <Route path="/forum" element={<Forum />} />
                  <Route path="/forum/create" element={<CreateTopic />} />
                  <Route path="/forum/topics/:id" element={<TopicDetail />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/about" element={<About />} />
                  <Route path="/pricing" element={<Pricing />} />
                  <Route path="/faq" element={<FAQ />} />
                  <Route path="/privacy" element={<Privacy />} />
                  <Route path="/terms" element={<Terms />} />
                  <Route path="/cookies" element={<Cookies />} />
                  <Route path="/status" element={<Status />} />
                  
                  {/* Protected Routes */}
                  <Route element={<ProtectedRoute />}>
                    <Route path="/profile" element={<UserProfile />} />
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/generate" element={<Generate />} />
                    <Route path="/analyze" element={<Analyze />} />
                    <Route path="/api-generation" element={<ApiGeneration />} />
                    <Route path="/api-docs" element={<ApiDocs />} />
                    <Route path="/project-analysis" element={<ProjectAnalysis />} />
                  </Route>
                </Routes>
              </Suspense>
            </main>
            <Footer />
            <Toaster
              position="top-right"
              toastOptions={{
                style: {
                  background: '#2d2d2d',
                  color: '#fff',
                  border: '1px solid #3c3c3c',
                },
              }}
            />
          </div>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  )
}

export default App