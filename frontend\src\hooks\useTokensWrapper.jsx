// Simple wrapper for useTokens to isolate import issues
import { useTokens } from './useTokens';

export const useTokensWrapper = () => {
  try {
    return useTokens();
  } catch (error) {
    console.error('useTokens error:', error);
    return {
      tokenInfo: null,
      loading: false,
      error: 'Failed to load token info',
      refreshTokenInfo: () => {},
      consumeTokens: () => {},
      refreshTokens: () => {},
      updateSubscription: () => {}
    };
  }
};
