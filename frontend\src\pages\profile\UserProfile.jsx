import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useTokens } from '../../hooks/useTokens';
import { FiUser, FiMail, FiEdit2, FiCheck, FiX, FiCreditCard, FiBarChart2, FiKey, FiCopy, FiRefreshCw } from 'react-icons/fi';
import toast from 'react-hot-toast';
import { getUserApiKey, regenerateUserApiKey, getUserProjects } from '../../api';

const UserProfile = () => {
  const navigate = useNavigate();
  const { user, updateProfile } = useAuth();
  const { tokenInfo, loading, error, updateSubscription, refreshTokenInfo } = useTokens();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: ''
  });
  const [api<PERSON><PERSON>, setApiKey] = useState('');
  const [apiKeyLoading, setApiKeyLoading] = useState(false);
  const [apiKeyError, setApiKeyError] = useState(null);
  const [projectCount, setProjectCount] = useState(0);

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.full_name || user.username || '',
        email: user.email || ''
      });
      fetchApiKey();
      fetchProjectCount();
    }
  }, [user]);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await updateProfile(formData);
      setIsEditing(false);
      toast.success('Profile updated successfully');
    } catch (error) {
      toast.error(error.message || 'Failed to update profile');
    }
  };

  const handlePlanUpgrade = async (plan) => {
    try {
      await updateSubscription(plan);
      toast.success('Subscription updated successfully');
    } catch (error) {
      toast.error(error.message || 'Failed to update subscription');
    }
  };

  const fetchApiKey = async () => {
    setApiKeyLoading(true);
    setApiKeyError(null);
    try {
      const res = await getUserApiKey();
      setApiKey(res.api_key);
    } catch (err) {
      setApiKeyError('Failed to load API key');
    } finally {
      setApiKeyLoading(false);
    }
  };

  const handleRegenerateApiKey = async () => {
    setApiKeyLoading(true);
    setApiKeyError(null);
    try {
      const res = await regenerateUserApiKey();
      setApiKey(res.api_key);
      toast.success('API key regenerated!');
    } catch (err) {
      setApiKeyError('Failed to regenerate API key');
    } finally {
      setApiKeyLoading(false);
    }
  };

  const handleCopyApiKey = () => {
    navigator.clipboard.writeText(apiKey);
    toast.success('API key copied to clipboard!');
  };

  const fetchProjectCount = async () => {
    try {
      const projects = await getUserProjects();
      setProjectCount(projects.length);
    } catch (err) {
      setProjectCount(0);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Please log in to view your profile</h2>
          <button
            onClick={() => navigate('/login')}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="bg-gray-800 rounded-lg shadow-xl overflow-hidden">
          <div className="p-6 sm:p-8">
            <div className="flex justify-between items-center mb-8">
              <h1 className="text-3xl font-bold text-white">Profile Dashboard</h1>
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                {isEditing ? (
                  <>
                    <FiX className="w-5 h-5" />
                    <span>Cancel</span>
                  </>
                ) : (
                  <>
                    <FiEdit2 className="w-5 h-5" />
                    <span>Edit Profile</span>
                  </>
                )}
              </button>
            </div>

            {/* Profile Information */}
            <div className="bg-gray-700 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <FiUser className="w-6 h-6 mr-2" />
                Profile Information
              </h2>
              {isEditing ? (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Name</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full bg-gray-600 border border-gray-500 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter your name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Email</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full bg-gray-600 border border-gray-500 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter your email"
                    />
                  </div>
                  <button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors"
                  >
                    Save Changes
                  </button>
                </form>
              ) : (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Name</label>
                    <p className="mt-1 text-white">{formData.name || 'Not set'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Email</label>
                    <p className="mt-1 text-white">{formData.email}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Token Usage Statistics */}
            <div className="bg-gray-700 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <FiCreditCard className="w-6 h-6 mr-2" />
                Token Usage
              </h2>

              <div className="mb-4 text-lg font-semibold">Projects Generated: {projectCount}</div>
              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              ) : error ? (
                <div className="text-red-500">{error}</div>
              ) : (
                <div className="space-y-6">
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-300 font-medium">Available Tokens</span>
                      <span className="text-sm text-cyan-200 font-bold">{((tokenInfo?.total_tokens ?? 0) - (tokenInfo?.used_tokens ?? 0))} / {tokenInfo?.total_tokens ?? 0}</span>
                    </div>
                    <div className="w-full bg-gray-600 rounded-full h-4 shadow-inner">
                      <div
                        className="bg-gradient-to-r from-cyan-400 to-blue-600 h-4 rounded-full transition-all duration-300 shadow-glow"
                        style={{ width: `${Math.min(100, Math.round(((tokenInfo?.total_tokens - tokenInfo?.used_tokens) / (tokenInfo?.total_tokens || 1)) * 100))}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-400 mt-2">
                      Included Requests: {tokenInfo?.total_tokens ?? 0} requests included in your plan. Last reset on: {tokenInfo?.last_refresh ? new Date(tokenInfo.last_refresh).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>

                  <div className="bg-gray-600 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-gray-300">Current Plan</h3>
                    <p className="mt-1 text-xl font-semibold text-white capitalize">
                      {tokenInfo?.subscription?.plan || 'Free'}
                    </p>
                  </div>

                  

                  <div className="bg-gray-600 rounded-lg p-4">
                    <h3 className="text-lg font-semibold mb-4 flex items-center">
                      <FiBarChart2 className="w-5 h-5 mr-2" />
                      Usage by Feature
                    </h3>
                    <div className="space-y-2">
                      {Object.entries(tokenInfo?.usageStats || {}).map(([feature, tokens]) => (
                        <div key={feature} className="flex justify-between items-center">
                          <span className="text-gray-300 capitalize">{feature}</span>
                          <span className="text-white font-medium">{tokens} tokens</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={() => navigate('/pricing')}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors"
                    >
                      Upgrade Plan
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* API Key Section */}
            <div className="bg-gray-700 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <FiKey className="w-6 h-6 mr-2" />
                API Key for VIKKI AI
              </h2>
              {apiKeyLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                  <span>Loading API key...</span>
                </div>
              ) : apiKeyError ? (
                <div className="text-red-500">{apiKeyError}</div>
              ) : (
                <div className="flex flex-col md:flex-row md:items-center md:space-x-4 space-y-2 md:space-y-0">
                  <div className="flex items-center bg-gray-800 rounded px-3 py-2 text-white font-mono text-sm break-all">
                    {apiKey}
                    <button onClick={handleCopyApiKey} className="ml-2 text-blue-400 hover:text-blue-600" title="Copy API Key">
                      <FiCopy className="w-5 h-5" />
                    </button>
                  </div>
                  <button
                    onClick={handleRegenerateApiKey}
                    className="flex items-center space-x-2 bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors mt-2 md:mt-0"
                    title="Regenerate API Key"
                  >
                    <FiRefreshCw className="w-5 h-5" />
                    <span>Regenerate</span>
                  </button>
                </div>
              )}
              <p className="text-gray-400 text-xs mt-2">Use this API key in your favorite IDE or extension to connect with VIKKI AI. Keep it secret!</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile; 