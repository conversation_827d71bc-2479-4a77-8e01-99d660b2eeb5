from fastapi import FastAP<PERSON>, HTTPException, Depends, Request, File, UploadFile, Form, APIRouter
from fastapi.security import OAuth2Password<PERSON>earer, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse, StreamingResponse
from bson import ObjectId
import motor.motor_asyncio
import os
from codestral_integration import generate_single_file_code, generate_code, parse_project_files, generate_code_streaming
from models.models import PromptRequest, ChatRequest, ChatResponse, Project, TokenUsage, TokenInfo
from models.model_manager import ModelManager
from utils import save_project_files, zip_project_folder
from passlib.context import CryptContext
from datetime import datetime, timedelta
from jose import JWTError, jwt
from typing import Optional, List, Dict, Literal
from pydantic import BaseModel
from dotenv import load_dotenv
from pathlib import Path
import shutil
import uuid
import tempfile
import logging
from fastapi.staticfiles import StaticFiles
# from auth import get_current_user, User, create_access_token, get_current_active_user
import json
from sse_starlette.sse import EventSourceResponse
import secrets

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
from analysis.analyzer import ProjectAnalyzer
from preview_system import PreviewManager, OutputPackager
from image_generation import ContentEnhancer
from payment_service import PaymentService, PLAN_CONFIGS, get_plan_config, upgrade_user_plan, cancel_user_subscription
from project_analyzer import ProjectAnalyzer

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="API Hub",
    description="A platform for generating and managing APIs",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",     # React dev server
        "https://vikki-ai.com",      # Production domain
        "https://www.vikki-ai.com"   # Production domain with www
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/token")

# MongoDB configuration
MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://localhost:27017/vikki-ai")
client = motor.motor_asyncio.AsyncIOMotorClient(MONGODB_URI)
db = client["vikki-ai"]

# Pydantic models
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None

class UserInDB(User):
    hashed_password: str

class UserCreate(BaseModel):
    username: str
    email: str
    password: str
    full_name: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "username": "johndoe",
                "email": "<EMAIL>",
                "password": "secretpassword",
                "full_name": "John Doe"
            }
        }

class GenerateRequest(BaseModel):
    prompt: str

class NewsletterSubscription(BaseModel):
    email: str

class LoginRequest(BaseModel):
    username: str
    password: str
    remember_me: bool = False

class ForgotPasswordRequest(BaseModel):
    email: str

class ResetPasswordRequest(BaseModel):
    token: str
    new_password: str

class ContactRequest(BaseModel):
    name: str
    email: str
    subject: str
    message: str
    captcha_token: str

class Project(BaseModel):
    id: str
    name: str
    description: str
    created_at: datetime
    files: List[str]
    user_id: str
    file_contents: Optional[Dict[str, str]] = None

# Helper functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

# Removed get_user function - using direct database lookup in get_current_user

async def authenticate_user(username: str, password: str):
    logger.info(f"Authenticating user: {username}")

    try:
        user_doc = await db.users.find_one({"username": username})
        if not user_doc:
            logger.warning(f"User not found: {username}")
            return False

        logger.info(f"User found: {username}, checking password")
        if not verify_password(password, user_doc["hashed_password"]):
            logger.warning(f"Invalid password for user: {username}")
            return False

        logger.info(f"Password verified for user: {username}")
        return UserInDB(**user_doc)
    except Exception as e:
        logger.error(f"Error authenticating user {username}: {str(e)}")
        return False

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception

    # Get user from database
    user_doc = await db.users.find_one({"username": token_data.username})
    if user_doc is None:
        raise credentials_exception

    # Convert to User model
    user = User(
        username=user_doc["username"],
        email=user_doc.get("email"),
        full_name=user_doc.get("full_name"),
        disabled=user_doc.get("disabled", False)
    )
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Enhanced login endpoint with remember me functionality
@app.post("/api/login")
async def enhanced_login(login_request: LoginRequest):
    logger.info(f"Enhanced login attempt for username: {login_request.username}")

    try:
        user = await authenticate_user(login_request.username, login_request.password)
        if not user:
            logger.warning(f"Authentication failed for username: {login_request.username}")
            raise HTTPException(
                status_code=401,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        logger.info(f"Authentication successful for username: {login_request.username}")

        # Set token expiration based on remember me
        if login_request.remember_me:
            access_token_expires = timedelta(days=30)  # 30 days for remember me
            logger.info(f"Remember me enabled for {login_request.username}, token expires in 30 days")
        else:
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)  # 30 minutes default

        access_token = create_access_token(
            data={"sub": user.username}, expires_delta=access_token_expires
        )

        # Update last login time
        await db.users.update_one(
            {"username": user.username},
            {"$set": {"last_login": datetime.utcnow()}}
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": int(access_token_expires.total_seconds()),
            "remember_me": login_request.remember_me
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error for username {login_request.username}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during authentication"
        )

# Original OAuth2 token endpoint (for compatibility)
@app.post("/api/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    logger.info(f"Login attempt for username: {form_data.username}")

    try:
        user = await authenticate_user(form_data.username, form_data.password)
        if not user:
            logger.warning(f"Authentication failed for username: {form_data.username}")
            raise HTTPException(
                status_code=401,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        logger.info(f"Authentication successful for username: {form_data.username}")
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username}, expires_delta=access_token_expires
        )
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error for username {form_data.username}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during authentication"
        )

# Forgot Password endpoint
@app.post("/api/forgot-password")
async def forgot_password(request: ForgotPasswordRequest):
    logger.info(f"Forgot password request for email: {request.email}")

    try:
        # Find user by email
        user_doc = await db.users.find_one({"email": request.email})
        if not user_doc:
            # Don't reveal if email exists or not for security
            logger.warning(f"Forgot password request for non-existent email: {request.email}")
            return {"message": "If the email exists, a password reset link has been sent."}

        # Generate reset token
        reset_token = secrets.token_urlsafe(32)
        reset_expires = datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour

        # Store reset token in database
        await db.password_resets.insert_one({
            "email": request.email,
            "token": reset_token,
            "expires_at": reset_expires,
            "used": False,
            "created_at": datetime.utcnow()
        })

        # In a real application, you would send an email here
        # For now, we'll just log the token (remove this in production)
        logger.info(f"Password reset token for {request.email}: {reset_token}")

        # TODO: Send email with reset link
        # send_password_reset_email(request.email, reset_token)

        return {"message": "If the email exists, a password reset link has been sent."}
    except Exception as e:
        logger.error(f"Error in forgot password for {request.email}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Reset Password endpoint
@app.post("/api/reset-password")
async def reset_password(request: ResetPasswordRequest):
    logger.info(f"Password reset attempt with token: {request.token[:8]}...")

    try:
        # Find valid reset token
        reset_doc = await db.password_resets.find_one({
            "token": request.token,
            "used": False,
            "expires_at": {"$gt": datetime.utcnow()}
        })

        if not reset_doc:
            logger.warning(f"Invalid or expired reset token: {request.token[:8]}...")
            raise HTTPException(
                status_code=400,
                detail="Invalid or expired reset token"
            )

        # Find user by email
        user_doc = await db.users.find_one({"email": reset_doc["email"]})
        if not user_doc:
            logger.error(f"User not found for email: {reset_doc['email']}")
            raise HTTPException(status_code=400, detail="User not found")

        # Update password
        hashed_password = get_password_hash(request.new_password)
        await db.users.update_one(
            {"email": reset_doc["email"]},
            {"$set": {"hashed_password": hashed_password}}
        )

        # Mark token as used
        await db.password_resets.update_one(
            {"token": request.token},
            {"$set": {"used": True, "used_at": datetime.utcnow()}}
        )

        logger.info(f"Password successfully reset for email: {reset_doc['email']}")
        return {"message": "Password has been successfully reset"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in reset password: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/api/register")
async def register_user(user: UserCreate):
    # Check if user already exists
    existing_user = await db.users.find_one({"username": user.username})
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already registered")
    
    # Create new user
    user_dict = user.dict()
    user_dict["hashed_password"] = get_password_hash(user_dict.pop("password"))
    user_dict["disabled"] = False  # Set default value for disabled field
    user_dict["full_name"] = user_dict.get("full_name")  # Ensure full_name is included
    
    # Insert user into database
    await db.users.insert_one(user_dict)
    
    return {"message": "User registered successfully"}

@app.get("/api/users/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    return current_user

@app.put("/api/users/me", response_model=User)
async def update_user_profile(
    profile_data: dict,
    current_user: User = Depends(get_current_active_user)
):
    try:
        # Update user in database
        update_fields = {}
        if "name" in profile_data:
            update_fields["full_name"] = profile_data["name"]
        if "email" in profile_data:
            update_fields["email"] = profile_data["email"]

        if update_fields:
            await db.users.update_one(
                {"username": current_user.username},
                {"$set": update_fields}
            )

        # Return updated user
        updated_user_doc = await db.users.find_one({"username": current_user.username})
        if updated_user_doc:
            return User(
                username=updated_user_doc["username"],
                email=updated_user_doc.get("email"),
                full_name=updated_user_doc.get("full_name"),
                disabled=updated_user_doc.get("disabled", False)
            )
        else:
            raise HTTPException(status_code=404, detail="User not found")
    except Exception as e:
        print(f"Error updating user profile: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/users/subscription")
async def update_user_subscription(
    subscription_data: dict,
    current_user: User = Depends(get_current_active_user)
):
    try:
        plan = subscription_data.get("plan", "free")

        # Update user subscription in database
        await db.users.update_one(
            {"username": current_user.username},
            {"$set": {"subscription.plan": plan}}
        )

        return {"message": "Subscription updated successfully", "plan": plan}
    except Exception as e:
        print(f"Error updating subscription: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Contact form endpoint
@app.post("/api/contact")
async def submit_contact_form(contact: ContactRequest):
    logger.info(f"Contact form submission from: {contact.email}")

    try:
        # In a real application, you would verify the hCaptcha token here
        # For now, we'll just check if it exists
        if not contact.captcha_token:
            raise HTTPException(status_code=400, detail="Captcha verification required")

        # Store contact message in database
        contact_doc = {
            "name": contact.name,
            "email": contact.email,
            "subject": contact.subject,
            "message": contact.message,
            "submitted_at": datetime.utcnow(),
            "status": "new"
        }

        await db.contact_messages.insert_one(contact_doc)

        # In a real application, you would send an email notification here
        logger.info(f"Contact message stored for {contact.email}")

        return {"message": "Message sent successfully! We'll get back to you soon."}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing contact form: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to send message")

# Newsletter subscription endpoint
@app.post("/api/newsletter/subscribe")
async def subscribe_newsletter(subscription: NewsletterSubscription):
    try:
        # Check if email already exists
        existing_subscription = await db.newsletter_subscribers.find_one({"email": subscription.email})
        if existing_subscription:
            return {"message": "Email already subscribed to newsletter"}

        # Add new subscription
        subscription_doc = {
            "email": subscription.email,
            "subscribed_at": datetime.utcnow(),
            "active": True
        }

        await db.newsletter_subscribers.insert_one(subscription_doc)

        return {"message": "Successfully subscribed to newsletter!"}
    except Exception as e:
        logger.error(f"Error subscribing to newsletter: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to subscribe to newsletter")

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.post("/api/generate")
async def generate_code_endpoint(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    try:
        # Check and deduct tokens for project generation (50 tokens per project)
        success, remaining_tokens, error_message = await check_and_deduct_tokens(
            current_user.username,
            "project_generation"
        )

        if not success:
            raise HTTPException(status_code=402, detail={
                "error": error_message,
                "remaining_tokens": remaining_tokens,
                "tokens_needed": TOKEN_COSTS["project_generation"],
                "upgrade_required": True
            })

        print(f"Project generation request from {current_user.username}, {remaining_tokens} tokens remaining")
        data = await request.json()
        prompt = data.get("prompt")
        model_type = data.get("model", "basic")  # Support model selection, default to 'basic'

        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")

        if not model_manager:
            raise HTTPException(status_code=500, detail="Model manager not initialized")

        # Generate project using ModelManager
        try:
            result = model_manager.generate_response(prompt, model_type)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Code generation failed: {str(e)}")

        if not result or "data" not in result or not result["data"].get("files"):
            raise HTTPException(status_code=500, detail="No files were generated for this prompt.")

        project_info = result["data"]["project"]
        files = result["data"]["files"]
        project_id = project_info["id"]

        return {
            "message": "Code generated successfully",
            "project_id": project_id,
            "files": [f["path"] for f in files],
            "download_url": f"/projects/{project_id}/download",
            "preview_url": f"/projects/{project_id}/preview"
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in generate_code_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/projects")
async def list_projects():
    projects = []
    async for proj in db.projects.find({}, {"prompt": 1}):
        projects.append({"id": str(proj["_id"]), "prompt": proj["prompt"]})
    return projects

@app.get("/api/projects/{project_id}")
async def get_project(project_id: str):
    proj = await db.projects.find_one({"_id": ObjectId(project_id)})
    if not proj:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"id": str(proj["_id"]), "prompt": proj["prompt"], "files": proj["files"]}

@app.get("/projects/{project_id}/download")
async def download_project(project_id: str, current_user: User = Depends(get_current_user)):
    try:
        project_dir = Path("generated_projects") / project_id
        if not project_dir.exists():
            raise HTTPException(status_code=404, detail="Project not found")
            
        # Read project info to verify ownership
        try:
            with open(project_dir / "project_info.json", "r") as f:
                project_info = json.loads(f.read())
        except:
            raise HTTPException(status_code=404, detail="Project info not found")
            
        if project_info.get("user_id") != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this project")
        
        # Create zip file
        zip_path = create_project_zip(project_id)
        if not zip_path or not os.path.exists(zip_path):
            raise HTTPException(status_code=500, detail="Failed to create zip file")
            
        return FileResponse(
            zip_path,
            media_type="application/zip",
            filename=f"project_{project_id}.zip"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in download_project: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def create_project_zip(project_id: str) -> str:
    """Create a zip file of the project directory"""
    try:
        project_dir = Path("generated_projects") / project_id
        zip_path = Path("generated_projects") / f"{project_id}.zip"
        
        # Remove existing zip if it exists
        if zip_path.exists():
            zip_path.unlink()
            
        # Create zip file
        shutil.make_archive(
            str(project_dir),  # Base name without extension
            'zip',            # Format
            project_dir       # Root directory
        )
        
        # Verify zip was created
        if not zip_path.exists():
            raise Exception("Failed to create zip file")
            
        return str(zip_path)
        
    except Exception as e:
        print(f"Error creating zip file: {str(e)}")
        raise

@app.post("/api/generate-files")
async def generate_project_files(req: PromptRequest):
    # example: split the project request into sub-prompts for backend, frontend, etc.
    prompts = [
        f"Generate backend code for: {req.prompt}",
        f"Generate frontend React Vite Tailwind CSS code for: {req.prompt}",
        f"Generate database models for: {req.prompt}"
    ]

    files = []
    for idx, p in enumerate(prompts):
        content = generate_single_file_code(p)
        files.append({
            "filename": f"file{idx+1}.py",  # adapt file extensions/names based on prompt
            "content": content
        })

    # Save to DB or return files directly
    return {"files": files}

# User related endpoints
@app.get("/users/stats")
async def get_user_stats(current_user: User = Depends(get_current_active_user)):
    user_stats = await db["users"].find_one(
        {"username": current_user.username},
        {"apiCalls": 1, "successRate": 1, "totalViews": 1, "projects": 1}
    )
    
    if not user_stats:
        return {
            "totalProjects": 0,
            "apiCalls": 0,
            "successRate": 100,
            "totalViews": 0
        }
    
    # Count total projects
    total_projects = len(user_stats.get("projects", []))
    
    return {
        "totalProjects": total_projects,
        "apiCalls": user_stats.get("apiCalls", 0),
        "successRate": user_stats.get("successRate", 100),
        "totalViews": user_stats.get("totalViews", 0)
    }

@app.get("/users/projects")
async def get_user_projects(current_user: User = Depends(get_current_user)):
    try:
        logger.info(f"Fetching projects for user: {current_user.username}")
        user_projects = []

        # Check if generated_projects directory exists, create if not
        projects_dir = Path("generated_projects")
        if not projects_dir.exists():
            logger.info("Generated projects directory does not exist, creating it")
            projects_dir.mkdir(exist_ok=True)
            return []

        # Get all project directories
        for project_dir in projects_dir.iterdir():
            if not project_dir.is_dir():
                continue

            # Check if this is a valid project directory
            project_info_file = project_dir / "project_info.json"
            if not project_info_file.exists():
                print(f"Skipping {project_dir.name}: no project_info.json")
                continue

            # Read project info
            try:
                with open(project_info_file, "r", encoding="utf-8") as f:
                    project_info = json.loads(f.read())

                # Only include projects for the current user
                if project_info.get("user_id") == current_user.username:
                    # Get list of files (with error handling)
                    try:
                        files = get_project_files(project_dir)
                        file_contents = read_project_files(project_dir)
                    except Exception as file_error:
                        print(f"Error reading files for project {project_dir.name}: {file_error}")
                        files = []
                        file_contents = {}

                    # Create project object
                    try:
                        created_at = datetime.fromisoformat(project_info.get("created_at", datetime.utcnow().isoformat()))
                    except ValueError:
                        created_at = datetime.utcnow()

                    project = Project(
                        id=project_dir.name,
                        name=project_info.get("name", f"Project {project_dir.name[:8]}"),
                        description=project_info.get("description", ""),
                        created_at=created_at,
                        files=files,
                        user_id=current_user.username,
                        file_contents=file_contents
                    )
                    user_projects.append(project)
            except json.JSONDecodeError as e:
                print(f"Error parsing project_info.json for {project_dir.name}: {e}")
                continue
            except Exception as e:
                print(f"Error processing project {project_dir.name}: {e}")
                continue

        logger.info(f"Returning {len(user_projects)} projects for user {current_user.username}")
        return user_projects
    except Exception as e:
        logger.error(f"Error in get_user_projects: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/projects/{project_id}/preview")
async def preview_project(project_id: str, current_user: User = Depends(get_current_user)):
    try:
        project_dir = Path("generated_projects") / project_id
        if not project_dir.exists():
            raise HTTPException(status_code=404, detail="Project not found")
            
        # Read project info
        try:
            with open(project_dir / "project_info.json", "r") as f:
                project_info = json.loads(f.read())
        except:
            raise HTTPException(status_code=404, detail="Project info not found")
            
        if project_info.get("user_id") != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this project")
            
        # Get files and their contents
        files = get_project_files(project_dir)
        file_contents = read_project_files(project_dir)
        
        return {
            "id": project_id,
            "name": project_info.get("name"),
            "description": project_info.get("description"),
            "created_at": project_info.get("created_at"),
            "files": files,
            "file_contents": file_contents,
            "download_url": f"/api/projects/{project_id}/download",
            "preview_url": f"/api/projects/{project_id}/preview"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def get_project_files(project_dir: Path) -> List[str]:
    """Get list of files in a project directory"""
    files = []
    for file_path in project_dir.rglob("*"):
        if file_path.is_file():
            files.append(str(file_path.relative_to(project_dir)))
    return files

def read_project_files(project_dir: Path) -> Dict[str, str]:
    """Read contents of all files in a project directory"""
    contents = {}
    for file_path in project_dir.rglob("*"):
        if file_path.is_file():
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    contents[str(file_path.relative_to(project_dir))] = f.read()
            except Exception as e:
                print(f"Error reading file {file_path}: {e}")
    return contents

# Initialize model manager
model_manager = None

@app.on_event("startup")
async def startup_event():
    global model_manager
    try:
        model_manager = ModelManager()
    except Exception as e:
        print(f"Error initializing model manager: {str(e)}")
        raise

@app.post("/api/chat", response_model=ChatResponse)
async def chat(request: ChatRequest, current_user: User = Depends(get_current_active_user)):
    try:
        # Check and deduct tokens for chat (10 tokens per chat)
        success, remaining_tokens, error_message = await check_and_deduct_tokens(
            current_user.username,
            "chat"
        )

        if not success:
            raise HTTPException(status_code=402, detail={
                "error": error_message,
                "remaining_tokens": remaining_tokens,
                "tokens_needed": TOKEN_COSTS["chat"],
                "upgrade_required": True
            })

        print(f"Chat request from {current_user.username}, {remaining_tokens} tokens remaining")

        # Check if the user is asking for file/project generation
        generation_keywords = [
            "create", "generate", "build", "make", "develop", "write",
            "project", "app", "website", "api", "component", "file",
            "code", "script", "function", "class", "module", "frontend",
            "backend", "full-stack", "react", "next.js", "fastapi", "django"
        ]

        is_generation_request = any(keyword in request.message.lower() for keyword in generation_keywords)

        if is_generation_request and not request.project_id:
            # This is a request to generate new files/project, not modify existing ones
            print(f"Detected generation request, using file generation system")
            try:
                # Use project generation tokens instead of chat tokens
                # First refund the chat tokens we already deducted
                await db.tokens.update_one(
                    {"user_id": current_user.username},
                    {"$inc": {"used_tokens": -TOKEN_COSTS["chat"]}}
                )

                # Now check for project generation tokens
                success, remaining_tokens, error_message = await check_and_deduct_tokens(
                    current_user.username,
                    "project_generation"
                )

                if not success:
                    raise HTTPException(status_code=402, detail={
                        "error": error_message,
                        "remaining_tokens": remaining_tokens,
                        "tokens_needed": TOKEN_COSTS["project_generation"],
                        "upgrade_required": True
                    })

                # Generate files using the streaming system
                files_generated = []
                project_id = None

                async for update in generate_code_streaming(request.message, request.model, user_id=current_user.username):
                    if update.get("type") == "file":
                        files_generated.append(update["data"]["file"])
                    elif update.get("type") == "complete":
                        project_id = update["data"]["project"]["id"]
                        break
                    elif update.get("type") == "error":
                        raise Exception(update["data"]["error"])

                if files_generated:
                    file_list = "\n".join([f"- {file['path']}" for file in files_generated])
                    response_text = f"✅ **Project Generated Successfully!**\n\n**Files Created:**\n{file_list}\n\n**Project ID:** {project_id}\n\nYou can now view, edit, and download your project files. Use the file explorer to navigate through your generated project structure."
                else:
                    response_text = "Project generation completed, but no files were created. Please try with a more specific prompt."

                return ChatResponse(response=response_text)

            except HTTPException:
                raise
            except Exception as e:
                print(f"Error in file generation: {str(e)}")
                # If file generation fails, fall back to regular chat
                response_text = f"I encountered an error while generating files: {str(e)}\n\nLet me provide you with guidance instead."

        if not model_manager:
            print("Error: Model manager not initialized")
            raise HTTPException(
                status_code=500,
                detail="AI models not properly initialized"
            )

        # Map frontend model names to backend model names
        model_mapping = {
            "basic": "basic",
            "advanced": "advanced",
            "vikki": "basic",
            "vikki_advanced": "advanced"
        }

        mapped_model = model_mapping.get(request.model, "basic")
        print(f"Processing chat request with model: {request.model} -> {mapped_model}")
        print(f"Message: {request.message}")
        print(f"Project context: {request.project_id}")

        try:
            # Check if this is a project-context chat
            if request.project_id and request.project_path:
                # This is a project-context chat - modify existing project
                print(f"Project-context chat for project: {request.project_id}")

                # Read existing project files for context
                project_context = ""
                project_dir = Path(f"generated_projects/{request.project_id}")
                if project_dir.exists():
                    # Get a summary of existing files
                    file_list = []
                    for file_path in project_dir.rglob("*"):
                        if file_path.is_file() and not file_path.name.startswith('.'):
                            rel_path = file_path.relative_to(project_dir)
                            file_list.append(str(rel_path))

                    project_context = f"Current project structure:\n" + "\n".join(file_list[:20])  # Limit to first 20 files

                # Create context-aware prompt
                context_prompt = f"""You are working on an existing project. Here's the current project structure:

{project_context}

User request: {request.message}

Please provide specific code changes, new files, or modifications needed. If creating new files, specify the exact file path and complete content. If modifying existing files, specify what changes to make.

Format your response as actionable instructions for the developer."""

                response = model_manager.generate_chat_response(
                    context_prompt,
                    mapped_model
                )
            else:
                # Regular chat without project context
                response = model_manager.generate_chat_response(
                    request.message,
                    mapped_model
                )

            print(f"Generated response: {response[:100]}...")  # Print first 100 chars
            return ChatResponse(response=response)
        except Exception as model_error:
            print(f"Error generating response: {str(model_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error generating response: {str(model_error)}"
            )
    except ValueError as e:
        print(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"Unexpected error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Removed old /stream-files endpoint - now using authenticated /api/stream-files

@app.post("/api/stream-files")
async def api_stream_files(request: Request, current_user: User = Depends(get_current_active_user)):
    """Stream file generation with authentication and token management"""
    try:
        body = await request.json()
        prompt = body.get("prompt")
        model_type = body.get("model", "basic")
        print(f"POST /api/stream-files: prompt={prompt}, model_type={model_type}, user={current_user.username}")

        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")

        # Check and deduct tokens for project generation (50 tokens)
        success, remaining_tokens, error_message = await check_and_deduct_tokens(
            current_user.username,
            "project_generation"
        )

        if not success:
            raise HTTPException(status_code=402, detail={
                "error": error_message,
                "remaining_tokens": remaining_tokens,
                "tokens_needed": TOKEN_COSTS["project_generation"],
                "upgrade_required": True
            })

        if not model_manager:
            raise HTTPException(status_code=500, detail="Model manager not initialized")

        async def event_generator():
            try:
                async for update in generate_code_streaming(prompt, model_type, user_id=current_user.username):
                    yield {
                        "event": "message",
                        "data": json.dumps(update)
                    }
            except Exception as e:
                print(f"Error in event_generator: {str(e)}")
                yield {
                    "event": "message",
                    "data": json.dumps({
                        "type": "error",
                        "data": {
                            "error": str(e)
                        }
                    })
                }

        return EventSourceResponse(event_generator())

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in api_stream_files: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Plan to token mapping (moved up before usage)
PLAN_TOKEN_LIMITS = {
    "free": 150,
    "pro": 500,
    "enterprise": 1000
}

# Token costs for different features
TOKEN_COSTS = {
    "project_generation": 50,
    "chat": 10,
    "code_analysis": 5,
    "file_modification": 15
}

# Helper function to check and deduct tokens
async def check_and_deduct_tokens(username: str, feature: str, custom_cost: Optional[int] = None):
    """
    Check if user has enough tokens and deduct them if available.
    Returns (success: bool, remaining_tokens: int, error_message: str)
    """
    try:
        # Get token cost for the feature
        tokens_needed = custom_cost if custom_cost else TOKEN_COSTS.get(feature, 10)

        # Get user's current token status
        user_tokens = await db.tokens.find_one({"user_id": username})
        if not user_tokens:
            return False, 0, "No token allocation found. Please contact support."

        remaining_tokens = user_tokens["total_tokens"] - user_tokens["used_tokens"]

        # Check if user has enough tokens
        if remaining_tokens < tokens_needed:
            return False, remaining_tokens, f"Insufficient tokens. Need {tokens_needed}, have {remaining_tokens}. Please upgrade your plan."

        # Deduct tokens
        usage_record = {
            "user_id": username,
            "feature": feature,
            "tokens_used": tokens_needed,
            "timestamp": datetime.utcnow(),
            "description": f"Used {tokens_needed} tokens for {feature}"
        }

        await db.tokens.update_one(
            {"user_id": username},
            {
                "$inc": {"used_tokens": tokens_needed},
                "$push": {"usage_history": usage_record}
            }
        )

        return True, remaining_tokens - tokens_needed, ""

    except Exception as e:
        print(f"Error in check_and_deduct_tokens: {str(e)}")
        return False, 0, f"Error processing tokens: {str(e)}"

# --- API Token management endpoints ---
tokens_router = APIRouter(prefix="/api/tokens", tags=["tokens"])

@tokens_router.get("/info")
async def api_get_token_info(current_user: User = Depends(get_current_active_user)):
    try:
        # Fetch user document to get the plan
        user_doc = await db.users.find_one({"username": current_user.username})
        plan = user_doc.get("subscription", {}).get("plan", "free") if user_doc else "free"
        total_tokens = PLAN_TOKEN_LIMITS.get(plan, 150)

        # Get user's token usage from database
        user_tokens = await db.tokens.find_one({"user_id": current_user.username})

        if not user_tokens:
            user_tokens = {
                "user_id": current_user.username,
                "total_tokens": total_tokens,
                "used_tokens": 0,
                "last_refresh": datetime.utcnow(),
                "usage_history": [],
                "plan": plan
            }
            await db.tokens.insert_one(user_tokens)
        else:
            # If plan changed, update total_tokens
            if user_tokens.get("total_tokens") != total_tokens:
                await db.tokens.update_one(
                    {"user_id": current_user.username},
                    {"$set": {"total_tokens": total_tokens}}
                )
                user_tokens["total_tokens"] = total_tokens

        remaining_tokens = user_tokens["total_tokens"] - user_tokens["used_tokens"]

        # Calculate time until next refresh is allowed
        last_refresh = user_tokens["last_refresh"]
        next_refresh_available = None
        can_refresh = True

        if isinstance(last_refresh, datetime):
            next_refresh_time = last_refresh + timedelta(hours=24)
            if datetime.utcnow() < next_refresh_time:
                can_refresh = False
                time_remaining = next_refresh_time - datetime.utcnow()
                hours_remaining = int(time_remaining.total_seconds() // 3600)
                minutes_remaining = int((time_remaining.total_seconds() % 3600) // 60)
                next_refresh_available = f"{hours_remaining}h {minutes_remaining}m"

        return {
            "total_tokens": user_tokens["total_tokens"],
            "used_tokens": user_tokens["used_tokens"],
            "remaining_tokens": remaining_tokens,
            "last_refresh": user_tokens["last_refresh"],
            "usage_history": user_tokens.get("usage_history", []),
            "plan": plan,
            "can_refresh": can_refresh,
            "next_refresh_available": next_refresh_available
        }
    except Exception as e:
        print(f"Error in token info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching token info: {str(e)}")

@tokens_router.post("/use")
async def api_use_tokens(
    usage: TokenUsage,
    current_user: User = Depends(get_current_active_user)
):
    try:
        user_tokens = await db.tokens.find_one({"user_id": current_user.username})
        if not user_tokens:
            raise HTTPException(status_code=400, detail="No token allocation found")
        remaining_tokens = user_tokens["total_tokens"] - user_tokens["used_tokens"]
        if remaining_tokens < usage.tokens_used:
            raise HTTPException(status_code=400, detail="Insufficient tokens")
        usage.user_id = current_user.username
        usage.timestamp = datetime.utcnow()
        await db.tokens.update_one(
            {"user_id": current_user.username},
            {
                "$inc": {"used_tokens": usage.tokens_used},
                "$push": {"usage_history": usage.dict()}
            }
        )
        return {"message": "Tokens used successfully", "remaining_tokens": remaining_tokens - usage.tokens_used}
    except Exception as e:
        print(f"Error using tokens: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error using tokens: {str(e)}")

@tokens_router.post("/refresh")
async def api_refresh_tokens(current_user: User = Depends(get_current_active_user)):
    try:
        user_tokens = await db.tokens.find_one({"user_id": current_user.username})
        if not user_tokens:
            # Create initial token allocation if it doesn't exist
            plan = "free"  # Default plan
            total_tokens = 150 if plan == "free" else 500

            user_tokens = {
                "user_id": current_user.username,
                "total_tokens": total_tokens,
                "used_tokens": 0,
                "last_refresh": datetime.utcnow(),
                "usage_history": [],
                "plan": plan
            }
            await db.tokens.insert_one(user_tokens)
            return {"message": "Token allocation created and refreshed", "total_tokens": total_tokens}

        last_refresh = user_tokens["last_refresh"]
        # Allow refresh if more than 24 hours have passed (instead of 30 days)
        if isinstance(last_refresh, datetime) and datetime.utcnow() - last_refresh < timedelta(hours=24):
            time_remaining = timedelta(hours=24) - (datetime.utcnow() - last_refresh)
            hours_remaining = int(time_remaining.total_seconds() // 3600)
            minutes_remaining = int((time_remaining.total_seconds() % 3600) // 60)
            raise HTTPException(
                status_code=400,
                detail=f"Tokens can be refreshed once per day. Try again in {hours_remaining}h {minutes_remaining}m"
            )

        await db.tokens.update_one(
            {"user_id": current_user.username},
            {
                "$set": {
                    "used_tokens": 0,
                    "last_refresh": datetime.utcnow()
                }
            }
        )
        return {"message": "Tokens refreshed successfully", "total_tokens": user_tokens["total_tokens"]}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error refreshing tokens: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error refreshing tokens: {str(e)}")

# Register the router
app.include_router(tokens_router)

# Forum endpoints (basic implementation)
@app.get("/api/forum/topics")
async def get_forum_topics():
    """Get forum topics - basic implementation"""
    try:
        # For now, return empty array since forum is not fully implemented
        return []
    except Exception as e:
        print(f"Error in forum topics: {str(e)}")
        return []

@app.get("/api/users/api-key")
async def get_api_key(current_user: User = Depends(get_current_active_user)):
    user_doc = await db.users.find_one({"username": current_user.username})
    if not user_doc:
        raise HTTPException(status_code=404, detail="User not found")
    api_key = user_doc.get("api_key")
    if not api_key:
        # Generate a new API key
        api_key = secrets.token_hex(32)
        await db.users.update_one({"username": current_user.username}, {"$set": {"api_key": api_key}})
    return {"api_key": api_key}

@app.post("/api/users/api-key/regenerate")
async def regenerate_api_key(current_user: User = Depends(get_current_active_user)):
    api_key = secrets.token_hex(32)
    await db.users.update_one({"username": current_user.username}, {"$set": {"api_key": api_key}})
    return {"api_key": api_key}

# Initialize the analyzer
project_analyzer = ProjectAnalyzer()

@app.post("/api/analyze")
async def analyze_project(files: list[UploadFile] = File(...)):
    temp_dir = Path("temp_uploads")
    temp_dir.mkdir(exist_ok=True)
    saved_files = []
    try:
        for file in files:
            file_path = temp_dir / file.filename
            with file_path.open("wb") as buffer:
                buffer.write(await file.read())
            with file_path.open("r", encoding="utf-8") as f:
                content = f.read()
            saved_files.append({"path": str(file_path), "content": content})
        # Analyze the project using ProjectAnalyzer (which can use mistral/codestral as needed)
        analysis_results = project_analyzer.analyze_project(saved_files)
        shutil.rmtree(temp_dir)
        return analysis_results
    except Exception as e:
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        raise HTTPException(status_code=500, detail=str(e))

# Initialize enhanced systems
preview_manager = PreviewManager()
output_packager = OutputPackager()
payment_service = PaymentService()
project_analyzer = ProjectAnalyzer()

@app.post("/api/preview/{project_id}")
async def create_preview(project_id: str, current_user: User = Depends(get_current_active_user)):
    """Create a live preview of the project"""
    try:
        project_dir = Path("generated_projects") / project_id
        if not project_dir.exists():
            raise HTTPException(status_code=404, detail="Project not found")

        # Check if user owns this project
        project_info_file = project_dir / "project_info.json"
        if project_info_file.exists():
            with open(project_info_file, "r") as f:
                project_info = json.load(f)

            if project_info.get("user_id") != current_user.username:
                raise HTTPException(status_code=403, detail="Access denied")

        preview_info = await preview_manager.create_preview(project_id, project_dir)
        return preview_info

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/preview/{project_id}/status")
async def get_preview_status(project_id: str, current_user: User = Depends(get_current_active_user)):
    """Get the status of a preview"""
    try:
        preview_info = preview_manager.get_preview_status(project_id)
        if not preview_info:
            raise HTTPException(status_code=404, detail="Preview not found")

        return preview_info

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/preview/{project_id}")
async def stop_preview(project_id: str, current_user: User = Depends(get_current_active_user)):
    """Stop a running preview"""
    try:
        success = await preview_manager.stop_preview(project_id)
        if success:
            return {"message": "Preview stopped successfully"}
        else:
            raise HTTPException(status_code=404, detail="Preview not found")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/package/{project_id}")
async def create_package(project_id: str, current_user: User = Depends(get_current_active_user)):
    """Create a downloadable package of the project"""
    try:
        project_dir = Path("generated_projects") / project_id
        if not project_dir.exists():
            raise HTTPException(status_code=404, detail="Project not found")

        # Check if user owns this project
        project_info_file = project_dir / "project_info.json"
        if project_info_file.exists():
            with open(project_info_file, "r") as f:
                project_info = json.load(f)

            if project_info.get("user_id") != current_user.username:
                raise HTTPException(status_code=403, detail="Access denied")

        package_info = await output_packager.create_package(project_id, project_dir)
        return package_info

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/download/{project_id}")
async def download_package(project_id: str, current_user: User = Depends(get_current_active_user)):
    """Download the packaged project"""
    try:
        package_path = Path("packaged_projects") / f"{project_id}.zip"
        if not package_path.exists():
            # Try to create package if it doesn't exist
            project_dir = Path("generated_projects") / project_id
            if project_dir.exists():
                await output_packager.create_package(project_id, project_dir)
            else:
                raise HTTPException(status_code=404, detail="Project not found")

        return FileResponse(
            path=str(package_path),
            filename=f"project_{project_id}.zip",
            media_type="application/zip"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Project Analysis Endpoints
@app.post("/api/analyze-project")
async def analyze_project_endpoint(
    zip_file: UploadFile = File(None),
    files: List[UploadFile] = File(None),
    analysis_type: str = Form(...),
    captcha_token: str = Form(...),
    current_user: User = Depends(get_current_active_user)
):
    """Analyze uploaded project files or ZIP archive"""
    try:
        # Verify hCaptcha token (simplified - in production, verify with hCaptcha API)
        if not captcha_token:
            raise HTTPException(status_code=400, detail="Captcha verification required")

        # Check token usage
        success, remaining_tokens, error_message = await check_and_deduct_tokens(
            current_user.username,
            "project_analysis"
        )

        if not success:
            raise HTTPException(status_code=402, detail={
                "error": error_message,
                "remaining_tokens": remaining_tokens,
                "tokens_needed": 15,
                "upgrade_required": True
            })

        if analysis_type == "zip" and zip_file:
            # Handle ZIP file analysis
            temp_zip_path = f"temp_{current_user.username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

            try:
                # Save uploaded ZIP file
                with open(temp_zip_path, "wb") as buffer:
                    shutil.copyfileobj(zip_file.file, buffer)

                # Analyze ZIP file
                analysis_result = await project_analyzer.extract_and_analyze_zip(
                    temp_zip_path,
                    current_user.username
                )

                return analysis_result

            finally:
                # Clean up temporary ZIP file
                if os.path.exists(temp_zip_path):
                    os.remove(temp_zip_path)

        elif analysis_type == "files" and files:
            # Handle individual files analysis
            temp_dir = tempfile.mkdtemp(prefix=f"analysis_{current_user.username}_")

            try:
                # Save uploaded files
                for file in files:
                    file_path = os.path.join(temp_dir, file.filename)
                    with open(file_path, "wb") as buffer:
                        shutil.copyfileobj(file.file, buffer)

                # Analyze project structure
                analysis_result = await project_analyzer.analyze_project_structure(temp_dir)

                # Store the project for later use
                project_id = project_analyzer.store_analyzed_project(temp_dir, current_user.username, analysis_result)
                analysis_result['project_id'] = project_id

                return analysis_result

            finally:
                # Clean up temporary directory
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir, ignore_errors=True)

        else:
            raise HTTPException(status_code=400, detail="Invalid analysis type or missing files")

    except Exception as e:
        logger.error(f"Project analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/chat")
async def chat_with_analysis(
    chat_request: dict,
    current_user: User = Depends(get_current_active_user)
):
    """Chat about analyzed project"""
    try:
        message = chat_request.get("message", "")
        project_id = chat_request.get("project_id", "")
        context = chat_request.get("context", "")

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        # Check token usage
        success, remaining_tokens, error_message = await check_and_deduct_tokens(
            current_user.username,
            "chat"
        )

        if not success:
            raise HTTPException(status_code=402, detail={
                "error": error_message,
                "remaining_tokens": remaining_tokens,
                "tokens_needed": 10,
                "upgrade_required": True
            })

        # Get project files for context if project_id is provided
        project_context = ""
        if project_id and context == "project_analysis":
            project_files = project_analyzer.get_project_files(project_id)
            if project_files:
                # Limit context to avoid token overflow
                file_summaries = []
                for file_path, content in list(project_files.items())[:5]:  # Limit to 5 files
                    file_summaries.append(f"File: {file_path}\n{content[:500]}...")
                project_context = "\n\n".join(file_summaries)

        # Create enhanced prompt with project context
        enhanced_prompt = f"""You are VIKKI AI, an expert code analysis and improvement assistant.

User's question: {message}

Project context:
{project_context}

Please provide helpful, specific advice about the code, suggest improvements, and answer the user's question. Be concise but thorough."""

        # Generate response using the model manager
        if not model_manager:
            raise HTTPException(status_code=500, detail="Model manager not initialized")

        try:
            result = model_manager.generate_response(enhanced_prompt, "basic")
            response = result.get("response", "I'm sorry, I couldn't generate a response.")
        except Exception as e:
            logger.error(f"Error generating chat response: {e}")
            response = "I'm sorry, I encountered an error while processing your request."

        return {"response": response}

    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Payment Endpoints
@app.get("/api/plans")
async def get_plans():
    """Get available subscription plans"""
    return {"plans": PLAN_CONFIGS}

@app.post("/api/payment/create-order")
async def create_payment_order(
    plan_data: dict,
    current_user: User = Depends(get_current_active_user)
):
    """Create a Razorpay order for subscription"""
    try:
        plan_name = plan_data.get("plan")
        if not plan_name or plan_name not in PLAN_CONFIGS:
            raise HTTPException(status_code=400, detail="Invalid plan")

        plan_config = PLAN_CONFIGS[plan_name]
        amount = plan_config["price"]

        if amount == 0:  # Free plan
            raise HTTPException(status_code=400, detail="Free plan doesn't require payment")

        order = payment_service.create_order(
            amount=amount,
            plan_name=plan_name,
            user_id=current_user.username
        )

        return order

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/payment/verify")
async def verify_payment(
    payment_data: dict,
    current_user: User = Depends(get_current_active_user)
):
    """Verify payment and upgrade user plan"""
    try:
        # Verify payment signature
        if not payment_service.verify_payment(payment_data):
            raise HTTPException(status_code=400, detail="Invalid payment signature")

        # Get plan from payment data
        plan_name = payment_data.get("plan", "pro")
        payment_id = payment_data.get("razorpay_payment_id")

        # Upgrade user plan
        success = await upgrade_user_plan(
            db=db,
            user_id=current_user.username,
            plan_name=plan_name,
            payment_id=payment_id
        )

        if not success:
            raise HTTPException(status_code=500, detail="Failed to upgrade plan")

        return {
            "success": True,
            "message": f"Successfully upgraded to {plan_name} plan",
            "plan": plan_name,
            "payment_id": payment_id
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

