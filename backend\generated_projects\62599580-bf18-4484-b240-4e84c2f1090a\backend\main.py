from fastapi import FastAPI
from fastapi.security import <PERSON>Auth2PasswordBearer, OAuth2PasswordRequestForm
from fastapi import Depends, HTTPException, status
from fastapi.responses import JSONResponse
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from models import Base, User, Message
from auth import Authenticator

app = FastAPI()
Base.metadata.create_all(binds={"sqlite": "sqlite:///./chat.db"})

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
authenticator = Authenticator()
