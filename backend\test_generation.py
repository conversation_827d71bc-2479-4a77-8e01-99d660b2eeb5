#!/usr/bin/env python3
"""
Test script to verify code generation improvements
"""
import sys
from pathlib import Path

def test_parsing():
    """Test the improved parsing function"""
    print("Testing improved parsing function...")
    
    # Sample generated text in the new format
    sample_text = """
// FILE: backend/main.py
// CONTENT:
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}
// END FILE

// FILE: frontend/package.json
// CONTENT:
{
  "name": "test-app",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.0.0"
  }
}
// END FILE

// FILE: README.md
// CONTENT:
# Test Application

This is a test application.

## Setup

1. Install dependencies
2. Run the application
// END FILE
"""
    
    try:
        from codestral_integration import parse_project_files
        files = parse_project_files(sample_text)
        
        print(f"✓ Parsed {len(files)} files successfully")
        
        for file in files:
            print(f"  - {file['path']} ({len(file['content'])} chars)")
            
        # Verify specific files
        backend_main = next((f for f in files if f['path'] == 'backend/main.py'), None)
        if backend_main:
            print("✓ Backend main.py found")
            if 'FastAPI' in backend_main['content']:
                print("✓ Backend main.py contains FastAPI code")
            else:
                print("✗ Backend main.py missing FastAPI code")
        else:
            print("✗ Backend main.py not found")
            
        frontend_package = next((f for f in files if f['path'] == 'frontend/package.json'), None)
        if frontend_package:
            print("✓ Frontend package.json found")
            if 'react' in frontend_package['content']:
                print("✓ Frontend package.json contains React dependency")
            else:
                print("✗ Frontend package.json missing React dependency")
        else:
            print("✗ Frontend package.json not found")
            
        return True
        
    except Exception as e:
        print(f"✗ Parsing test failed: {e}")
        return False

def test_chat_models():
    """Test if chat models are working"""
    print("\nTesting chat model availability...")
    
    try:
        from models.model_manager import ModelManager
        mm = ModelManager()
        
        # Test basic model
        try:
            basic_model = mm.get_model("basic")
            print("✓ Basic model (Mistral) available")
        except Exception as e:
            print(f"✗ Basic model failed: {e}")
            
        # Test advanced model
        try:
            advanced_model = mm.get_model("advanced")
            print("✓ Advanced model (Codestral) available")
        except Exception as e:
            print(f"✗ Advanced model failed: {e}")
            
        return True
        
    except Exception as e:
        print(f"✗ Model manager test failed: {e}")
        return False

if __name__ == "__main__":
    print("=== Code Generation Test Suite ===")
    
    # Test 1: Parsing function
    parsing_ok = test_parsing()
    
    # Test 2: Model availability
    models_ok = test_chat_models()
    
    print("\n=== Test Results ===")
    print(f"Parsing function: {'✓' if parsing_ok else '✗'}")
    print(f"Model availability: {'✓' if models_ok else '✗'}")
    
    if parsing_ok and models_ok:
        print("\n🎉 All tests passed! Code generation should work better now.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        sys.exit(1)
