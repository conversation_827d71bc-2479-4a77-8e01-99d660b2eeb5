import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { FiUpload, FiFile, FiX, FiCheck, FiAlertCircle, FiArchive, FiMessageCircle, FiCode, FiShield, FiZap, FiTrendingUp } from 'react-icons/fi'
import toast from 'react-hot-toast'
import PageContainer from '../components/layout/PageContainer'
import HCaptcha from '../components/HCaptcha'
import { useNavigate } from 'react-router-dom'

const Analyze = () => {
  const [files, setFiles] = useState([])
  const [analysis, setAnalysis] = useState(null)
  const [loading, setLoading] = useState(false)
  const [captchaToken, setCaptchaToken] = useState('')
  const [showChat, setShowChat] = useState(false)
  const [chatMessages, setChatMessages] = useState([])
  const [chatInput, setChatInput] = useState('')
  const [chatLoading, setChatLoading] = useState(false)
  const navigate = useNavigate()

  const onDrop = useCallback((acceptedFiles) => {
    // Check if ZIP file is uploaded
    const zipFiles = acceptedFiles.filter(file => file.name.endsWith('.zip'))
    const otherFiles = acceptedFiles.filter(file => !file.name.endsWith('.zip'))

    if (zipFiles.length > 0) {
      // If ZIP file is uploaded, replace all files with just the ZIP
      setFiles(zipFiles)
      if (otherFiles.length > 0) {
        toast.info('ZIP file detected. Other files will be ignored.')
      }
    } else {
      setFiles(prev => [...prev, ...otherFiles])
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/zip': ['.zip'],
      'text/*': ['.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.h', '.hpp', '.cs', '.go', '.rs', '.php', '.rb', '.swift', '.kt', '.scala', '.html', '.css', '.scss', '.json', '.xml', '.yaml', '.yml', '.md', '.txt']
    },
    maxFiles: 1 // Only allow one file at a time for ZIP uploads
  })

  const removeFile = (index) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleAnalyze = async () => {
    if (files.length === 0) {
      toast.error('Please upload at least one file')
      return
    }

    if (!captchaToken) {
      toast.error('Please complete the captcha verification')
      return
    }

    setLoading(true)
    try {
      const formData = new FormData()

      // Check if it's a ZIP file
      const isZipFile = files[0].name.endsWith('.zip')

      if (isZipFile) {
        formData.append('zip_file', files[0])
        formData.append('analysis_type', 'zip')
      } else {
        files.forEach(file => {
          formData.append('files', file)
        })
        formData.append('analysis_type', 'files')
      }

      formData.append('captcha_token', captchaToken)

      const response = await fetch('/api/analyze-project', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Analysis failed')
      }

      const data = await response.json()
      setAnalysis(data)
      setShowChat(true)
      toast.success('Analysis completed successfully!')

      // Initialize chat with analysis summary
      setChatMessages([
        {
          role: 'assistant',
          content: `I've analyzed your project! Here's a summary:

📊 **Project Overview:**
- ${data.file_count} files analyzed
- ${Object.keys(data.languages).length} programming languages detected
- Code quality score: ${data.code_quality?.score || 'N/A'}/100

🔍 **Key Findings:**
- ${data.recommendations?.length || 0} recommendations
- ${data.security_issues?.length || 0} security issues
- ${data.performance_suggestions?.length || 0} performance suggestions

How can I help you improve your project?`
        }
      ])

    } catch (error) {
      console.error('Analysis error:', error)
      toast.error(error.message || 'Failed to analyze project')
    } finally {
      setLoading(false)
    }
  }

  const handleCaptchaVerify = (token) => {
    setCaptchaToken(token)
  }

  const handleChatSend = async () => {
    if (!chatInput.trim() || !analysis) return

    const userMessage = chatInput.trim()
    setChatInput('')
    setChatLoading(true)

    // Add user message to chat
    setChatMessages(prev => [...prev, { role: 'user', content: userMessage }])

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          message: userMessage,
          project_id: analysis.project_id,
          context: 'project_analysis'
        })
      })

      if (!response.ok) {
        throw new Error('Failed to get response')
      }

      const data = await response.json()

      // Add assistant response to chat
      setChatMessages(prev => [...prev, { role: 'assistant', content: data.response }])

    } catch (error) {
      console.error('Chat error:', error)
      toast.error('Failed to get response')
      setChatMessages(prev => [...prev, { role: 'assistant', content: 'Sorry, I encountered an error. Please try again.' }])
    } finally {
      setChatLoading(false)
    }
  }

  const handleGenerateImprovedProject = () => {
    if (!analysis) return

    // Navigate to generate page with analysis context
    navigate('/generate', {
      state: {
        analysisContext: analysis,
        prompt: `Improve this project based on the analysis. Address the following issues: ${analysis.recommendations?.slice(0, 3).join(', ')}`
      }
    })
  }

  const renderAnalysis = () => {
    if (!analysis) return null

    return (
      <div className="space-y-8">
        {/* Project Overview */}
        <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
          <div className="flex items-center gap-3 mb-6">
            <FiCode className="text-3xl text-cyan-400" />
            <h3 className="text-2xl font-bold text-white">Project Overview</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-gradient-to-br from-cyan-900/20 to-blue-900/20 backdrop-blur-sm border border-cyan-500/20 rounded-xl p-6 text-center">
              <p className="text-gray-300 mb-2">Total Files</p>
              <p className="text-3xl font-bold text-cyan-400">{analysis.file_count || 0}</p>
            </div>
            <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm border border-blue-500/20 rounded-xl p-6 text-center">
              <p className="text-gray-300 mb-2">Project Size</p>
              <p className="text-3xl font-bold text-blue-400">{Math.round((analysis.total_size || 0) / 1024)}KB</p>
            </div>
            <div className="bg-gradient-to-br from-purple-900/20 to-pink-900/20 backdrop-blur-sm border border-purple-500/20 rounded-xl p-6 text-center">
              <p className="text-gray-300 mb-2">Languages</p>
              <p className="text-3xl font-bold text-purple-400">{Object.keys(analysis.languages || {}).length}</p>
            </div>
            <div className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 backdrop-blur-sm border border-green-500/20 rounded-xl p-6 text-center">
              <p className="text-gray-300 mb-2">Quality Score</p>
              <p className="text-3xl font-bold text-green-400">{analysis.code_quality?.score || 'N/A'}/100</p>
            </div>
          </div>

          {/* Detected Frameworks */}
          {analysis.detected_frameworks && analysis.detected_frameworks.length > 0 && (
            <div className="mt-6">
              <h4 className="text-lg font-semibold text-white mb-3">Detected Frameworks</h4>
              <div className="flex flex-wrap gap-2">
                {analysis.detected_frameworks.map((framework, index) => (
                  <span key={index} className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 text-cyan-300 px-3 py-1 rounded-full text-sm font-medium">
                    {framework}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Recommendations */}
        {analysis.recommendations && analysis.recommendations.length > 0 && (
          <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
            <div className="flex items-center gap-3 mb-6">
              <FiTrendingUp className="text-3xl text-green-400" />
              <h3 className="text-2xl font-bold text-white">Recommendations</h3>
            </div>
            <div className="space-y-4">
              {analysis.recommendations.map((recommendation, index) => (
                <div key={index} className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 backdrop-blur-sm border border-green-500/20 rounded-xl p-6">
                  <div className="flex items-start gap-4">
                    <FiCheck className="text-green-400 mt-1 flex-shrink-0" />
                    <p className="text-gray-300 leading-relaxed">{recommendation}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Improvements */}
        {analysis.improvements && analysis.improvements.length > 0 && (
          <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
            <div className="flex items-center gap-3 mb-6">
              <FiZap className="text-3xl text-yellow-400" />
              <h3 className="text-2xl font-bold text-white">Suggested Improvements</h3>
            </div>
            <div className="space-y-4">
              {analysis.improvements.map((improvement, index) => (
                <div key={index} className="bg-gradient-to-r from-yellow-900/20 to-orange-900/20 backdrop-blur-sm border border-yellow-500/20 rounded-xl p-6">
                  <div className="flex items-start gap-4">
                    <FiAlertCircle className="text-yellow-400 mt-1 flex-shrink-0" />
                    <p className="text-gray-300 leading-relaxed">{improvement}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Performance Suggestions */}
        {analysis.performance_suggestions && analysis.performance_suggestions.length > 0 && (
          <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
            <div className="flex items-center gap-3 mb-6">
              <FiZap className="text-3xl text-blue-400" />
              <h3 className="text-2xl font-bold text-white">Performance Suggestions</h3>
            </div>
            <div className="space-y-4">
              {analysis.performance_suggestions.map((suggestion, index) => (
                <div key={index} className="bg-gradient-to-r from-blue-900/20 to-indigo-900/20 backdrop-blur-sm border border-blue-500/20 rounded-xl p-6">
                  <div className="flex items-start gap-4">
                    <FiZap className="text-blue-400 mt-1 flex-shrink-0" />
                    <p className="text-gray-300 leading-relaxed">{suggestion}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Security Issues */}
        {analysis.security_issues && analysis.security_issues.length > 0 && (
          <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
            <div className="flex items-center gap-3 mb-6">
              <FiShield className="text-3xl text-red-400" />
              <h3 className="text-2xl font-bold text-white">Security Issues</h3>
            </div>
            <div className="space-y-4">
              {analysis.security_issues.map((issue, index) => (
                <div key={index} className="bg-gradient-to-r from-red-900/20 to-pink-900/20 backdrop-blur-sm border border-red-500/20 rounded-xl p-6">
                  <div className="flex items-start gap-4">
                    <FiAlertCircle className="text-red-400 mt-1 flex-shrink-0" />
                    <p className="text-gray-300 leading-relaxed">{issue}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleGenerateImprovedProject}
            className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105"
          >
            Generate Improved Project
          </button>
          <button
            onClick={() => setShowChat(!showChat)}
            className="border-2 border-gray-600 hover:border-cyan-400 text-gray-300 hover:text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:bg-cyan-400/10"
          >
            {showChat ? 'Hide Chat' : 'Chat About Analysis'}
          </button>
        </div>
      </div>
    )
  }

  return (
    <PageContainer>
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] text-white font-sans py-32" style={{ fontFamily: 'Inter, Space Grotesk, sans-serif' }}>
        <div className="space-y-12 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-5xl md:text-7xl font-extrabold bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 text-transparent bg-clip-text drop-shadow-glow tracking-tight mb-6">
              Project Analysis
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 font-light max-w-4xl mx-auto leading-relaxed">
              Upload your project files or ZIP archive for comprehensive analysis, recommendations, and AI-powered improvements
            </p>
          </div>
          {/* File Upload Area */}
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-3xl p-16 text-center cursor-pointer transition-all duration-300 bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm hover:border-cyan-500/50 ${
              isDragActive ? 'border-cyan-400 bg-cyan-900/20 scale-105' : 'border-gray-700/50'
            }`}
          >
            <input {...getInputProps()} />
            <div className="mb-8">
              {files.length > 0 && files[0].name.endsWith('.zip') ? (
                <FiArchive className="mx-auto h-20 w-20 text-purple-400 animate-bounce" />
              ) : (
                <FiUpload className="mx-auto h-20 w-20 text-cyan-400 animate-bounce" />
              )}
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">
              {isDragActive ? 'Drop your files here!' : 'Upload Project Files'}
            </h3>
            <p className="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">
              Drag and drop your project files or ZIP archive, or click to browse
            </p>
            <div className="bg-gradient-to-r from-cyan-900/20 to-blue-900/20 backdrop-blur-sm border border-cyan-500/20 rounded-xl p-4 max-w-4xl mx-auto">
              <p className="text-sm text-gray-400 mb-2">
                <strong className="text-cyan-400">📁 ZIP Files:</strong> Upload entire projects for comprehensive analysis
              </p>
              <p className="text-sm text-gray-400">
                <strong className="text-blue-400">📄 Individual Files:</strong> .js, .jsx, .ts, .tsx, .py, .java, .cpp, .html, .css, .json, .md, and more
              </p>
            </div>
          </div>
          {/* Selected Files */}
          {files.length > 0 && (
            <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-white mb-6">Selected Files</h3>
              <div className="space-y-4">
                {files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-gradient-to-r from-gray-800/50 to-gray-700/30 p-4 rounded-xl border border-gray-600/30">
                    <div className="flex items-center gap-4">
                      {file.name.endsWith('.zip') ? (
                        <FiArchive className="text-purple-400 text-xl" />
                      ) : (
                        <FiFile className="text-cyan-400 text-xl" />
                      )}
                      <div>
                        <span className="text-white font-medium">{file.name}</span>
                        <p className="text-gray-400 text-sm">
                          {(file.size / 1024).toFixed(1)} KB
                          {file.name.endsWith('.zip') && ' • ZIP Archive'}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => removeFile(index)}
                      className="text-red-400 hover:text-red-300 transition-colors p-2 hover:bg-red-400/10 rounded-lg"
                    >
                      <FiX className="text-lg" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* hCaptcha */}
          {files.length > 0 && (
            <div className="flex justify-center">
              <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                <h3 className="text-lg font-semibold text-white mb-4 text-center">Security Verification</h3>
                <HCaptcha
                  onVerify={handleCaptchaVerify}
                  onError={() => toast.error('Captcha verification failed')}
                  onExpire={() => setCaptchaToken('')}
                  theme="dark"
                />
              </div>
            </div>
          )}
          {/* Analyze Button */}
          <div className="text-center">
            <button
              onClick={handleAnalyze}
              disabled={loading || files.length === 0 || !captchaToken}
              className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-12 py-4 rounded-xl font-bold text-lg shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none focus:outline-none focus:ring-4 focus:ring-cyan-400/50"
            >
              {loading ? (
                <div className="flex items-center gap-3">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-cyan-400"></div>
                  Analyzing Project...
                </div>
              ) : (
                'Analyze Project'
              )}
            </button>
            {files.length > 0 && !captchaToken && (
              <p className="text-gray-400 text-sm mt-2">Please complete the captcha verification above</p>
            )}
          </div>
          {/* Analysis Results */}
          {analysis && (
            <div className="space-y-12">
              {renderAnalysis()}
            </div>
          )}

          {/* Chat Interface */}
          {showChat && analysis && (
            <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
              <div className="flex items-center gap-3 mb-6">
                <FiMessageCircle className="text-3xl text-cyan-400" />
                <h3 className="text-2xl font-bold text-white">Chat with AI about your project</h3>
              </div>

              {/* Chat Messages */}
              <div className="bg-gray-800/50 rounded-xl p-6 mb-6 max-h-96 overflow-y-auto">
                {chatMessages.map((message, index) => (
                  <div key={index} className={`mb-4 ${message.role === 'user' ? 'text-right' : 'text-left'}`}>
                    <div className={`inline-block max-w-3xl p-4 rounded-xl ${
                      message.role === 'user'
                        ? 'bg-gradient-to-r from-cyan-500 to-blue-600 text-white'
                        : 'bg-gradient-to-r from-gray-700 to-gray-600 text-gray-100'
                    }`}>
                      <pre className="whitespace-pre-wrap font-sans">{message.content}</pre>
                    </div>
                  </div>
                ))}
                {chatLoading && (
                  <div className="text-left mb-4">
                    <div className="inline-block bg-gradient-to-r from-gray-700 to-gray-600 text-gray-100 p-4 rounded-xl">
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-400"></div>
                        <span>AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Chat Input */}
              <div className="flex gap-4">
                <input
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleChatSend()}
                  placeholder="Ask about your project analysis, request improvements, or get coding advice..."
                  className="flex-1 bg-gray-800/50 border border-gray-600 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                  disabled={chatLoading}
                />
                <button
                  onClick={handleChatSend}
                  disabled={!chatInput.trim() || chatLoading}
                  className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-6 py-3 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  Send
                </button>
              </div>
            </div>
          )}
        </div>
        <style>{`.drop-shadow-glow { filter: drop-shadow(0 0 6px #00fff7cc); }`}</style>
      </div>
    </PageContainer>
  )
}

export default Analyze 