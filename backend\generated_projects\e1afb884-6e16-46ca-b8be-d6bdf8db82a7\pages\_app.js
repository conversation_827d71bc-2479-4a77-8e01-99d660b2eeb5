import type { AppProps } from 'next/app'
import 'styles/globals.css'
import type { Element } from 'react'
import type { AppContext, Props } from 'next/app'
import type { StripeProvider, Elements } from 'react-stripe-elements'
import { loadStripe } from '@stripe/stripe-js'
const stripePromise = loadStripe(process.env.STRIPE_PUBLISHABLE_KEY!)
function MyApp({ Component, pageProps }: AppProps) {
return (
<StripeProvider stripe={stripePromise}>
<Elements stripe={stripePromise}>
<Component {...pageProps} />
</Elements>
</StripeProvider>
)
}
export default MyApp