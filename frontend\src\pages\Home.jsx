import { useNavigate } from 'react-router-dom'
import { FiCode, FiDatabase, FiLayers, FiUpload, FiSearch, FiZap, FiImage, FiEye, FiDownload, FiGitBranch } from 'react-icons/fi'
import PageContainer from '../components/layout/PageContainer'

const Home = () => {
  const navigate = useNavigate()

  const features = [
    {
      icon: <FiCode className="w-8 h-8" />,
      title: "AI-Powered Code Generation",
      description: "Generate complete full-stack applications from simple text prompts. React, Next.js, FastAPI, Django - all supported."
    },
    {
      icon: <FiImage className="w-8 h-8" />,
      title: "Image Generation",
      description: "Automatically generate relevant images for your projects using Stable Diffusion integration."
    },
    {
      icon: <FiEye className="w-8 h-8" />,
      title: "Live Preview",
      description: "See your applications running instantly with our containerized preview system."
    },
    {
      icon: <FiDownload className="w-8 h-8" />,
      title: "Complete Packages",
      description: "Download production-ready projects with deployment guides and setup scripts."
    },
    {
      icon: <FiDatabase className="w-8 h-8" />,
      title: "Smart Framework Detection",
      description: "AI automatically detects your preferred frameworks and generates appropriate code structure."
    },
    {
      icon: <FiGitBranch className="w-8 h-8" />,
      title: "Version Control Ready",
      description: "Generated projects include proper .gitignore, README, and deployment configurations."
    }
  ]

  const quickActions = [
    {
      title: "Generate Code",
      description: "Create new applications and APIs",
      icon: <FiCode className="w-8 h-8" />,
      action: () => navigate('/generate')
    },
    {
      title: "Analyze Project",
      description: "Get detailed code analysis",
      icon: <FiSearch className="w-8 h-8" />,
      action: () => navigate('/analyze')
    },
    {
      title: "Upload Project",
      description: "Upload existing code for analysis",
      icon: <FiUpload className="w-8 h-8" />,
      action: () => navigate('/analyze')
    }
  ]

  return (
    <PageContainer>
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] text-white font-sans pt-20" style={{ fontFamily: 'Inter, Space Grotesk, sans-serif' }}>
        {/* Hero Section */}
        <div className="relative overflow-hidden py-32 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            {/* Animated background elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute -top-40 -right-40 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
            </div>

            <div className="relative z-10">
            <h1 className="text-6xl md:text-8xl lg:text-[10rem] font-black mb-8 tracking-tight leading-none text-[#00FFFF] drop-shadow-[0_0_10px_#00FFFF]">
              VIKKI AI
            </h1>


              <p className="text-2xl md:text-3xl lg:text-4xl text-gray-300 mb-6 max-w-5xl mx-auto font-light leading-relaxed">
                Transform Ideas into Production-Ready Applications
              </p>
              <p className="text-lg md:text-xl text-gray-400 mb-12 max-w-4xl mx-auto leading-relaxed">
                Your AI-powered development partner. Generate complete full-stack applications,
                APIs, and backend systems with images, live previews, and deployment-ready packages
                — all from a single prompt.
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-6 mb-16">
                <button
                  onClick={() => navigate('/generate')}
                  className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-12 py-4 rounded-2xl font-bold text-xl shadow-2xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-cyan-400/50"
                >
                  Start Building Now
                </button>
                <button
                  onClick={() => navigate('/api-docs')}
                  className="border-2 border-gray-600 hover:border-cyan-400 text-gray-300 hover:text-white px-12 py-4 rounded-2xl font-bold text-xl transition-all duration-300 hover:bg-cyan-400/10 focus:outline-none focus:ring-4 focus:ring-cyan-400/50"
                >
                  View API Docs
                </button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-cyan-400 mb-2">10K+</div>
                  <div className="text-gray-400">Projects Generated</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-blue-400 mb-2">5K+</div>
                  <div className="text-gray-400">Happy Developers</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-purple-400 mb-2">15+</div>
                  <div className="text-gray-400">Frameworks</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-green-400 mb-2">99.9%</div>
                  <div className="text-gray-400">Uptime</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* What is VIKKI AI Section */}
        <div className="py-32 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-20">
              <h2 className="text-2xl md:text-6xl font-black mb-8 tracking-tight leading-none text-[#e6ecec] ">
                What is VIKKI AI?
              </h2>
              <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                VIKKI AI is your intelligent development partner that transforms natural language
                into production-ready applications. From idea to deployment in minutes, not months.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div className="space-y-8">
                <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                  <h3 className="text-2xl font-bold text-white mb-4">🚀 Complete Full-Stack Generation</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Generate React frontends, FastAPI backends, database models, authentication systems,
                    and Docker configurations - all from a single prompt.
                  </p>
                </div>

                <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                  <h3 className="text-2xl font-bold text-white mb-4">🎨 AI-Generated Images</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Automatically create relevant images, logos, and graphics for your projects
                    using integrated Stable Diffusion technology.
                  </p>
                </div>

                <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                  <h3 className="text-2xl font-bold text-white mb-4">👁️ Live Preview & Download</h3>
                  <p className="text-gray-300 leading-relaxed">
                    See your applications running instantly with live previews, then download
                    complete packages with deployment guides.
                  </p>
                </div>
              </div>

              <div className="relative">
                <div className="bg-gradient-to-br from-cyan-900/20 to-blue-900/20 backdrop-blur-sm border border-cyan-500/20 rounded-3xl p-8 md:p-12">
                  <div className="space-y-6">
                    <div className="flex items-center gap-4">
                      <div className="w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-gray-300">Analyzing prompt...</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="w-4 h-4 bg-blue-400 rounded-full animate-pulse delay-300"></div>
                      <span className="text-gray-300">Generating frontend...</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="w-4 h-4 bg-purple-400 rounded-full animate-pulse delay-500"></div>
                      <span className="text-gray-300">Creating backend...</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="w-4 h-4 bg-yellow-400 rounded-full animate-pulse delay-700"></div>
                      <span className="text-gray-300">Adding images...</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="w-4 h-4 bg-cyan-400 rounded-full animate-pulse delay-1000"></div>
                      <span className="text-gray-300">Creating preview...</span>
                    </div>
                    <div className="mt-8 p-4 bg-green-500/10 border border-green-500/20 rounded-xl">
                      <div className="text-green-400 font-bold">✅ Project Generated Successfully!</div>
                      <div className="text-gray-300 text-sm mt-2">Ready for download and deployment</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Features Section */}
        <div className="py-32 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-6xl font-bold mb-8 text-white">
                Powerful Features
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Everything you need to build modern applications, powered by advanced AI
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="group bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-cyan-500/50 transition-all duration-300 hover:transform hover:scale-105"
                >
                  <div className="text-cyan-400 mb-6 group-hover:text-cyan-300 transition-colors">
                    {feature.icon}
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-cyan-100 transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* Quick Actions */}
        <div className="py-32 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-6xl font-bold mb-8 text-white">
                Get Started in Seconds
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Choose your path to building amazing applications
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.action}
                  className="group bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-cyan-500/50 transition-all duration-300 hover:transform hover:scale-105 text-left"
                >
                  <div className="text-cyan-400 mb-6 text-5xl group-hover:text-cyan-300 transition-colors">
                    {action.icon}
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-cyan-100 transition-colors">
                    {action.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors">
                    {action.description}
                  </p>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="py-32 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-cyan-900/20 to-blue-900/20 backdrop-blur-sm border border-cyan-500/20 rounded-3xl p-16 text-center">
              <h2 className="text-4xl md:text-6xl font-bold text-white mb-8">
                Ready to Build the Future?
              </h2>
              <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed">
                Join thousands of developers who are already using VIKKI AI to build
                amazing applications faster than ever before.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <button
                  onClick={() => navigate('/generate')}
                  className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-12 py-4 rounded-2xl font-bold text-xl shadow-2xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-cyan-400/50"
                >
                  Start Building Now
                </button>
                <button
                  onClick={() => navigate('/pricing')}
                  className="border-2 border-gray-600 hover:border-cyan-400 text-gray-300 hover:text-white px-12 py-4 rounded-2xl font-bold text-xl transition-all duration-300 hover:bg-cyan-400/10 focus:outline-none focus:ring-4 focus:ring-cyan-400/50"
                >
                  View Pricing
                </button>
              </div>
            </div>
          </div>
        </div>

        <style>{`
          .drop-shadow-glow { filter: drop-shadow(0 0 6px #00fff7cc); }
        `}</style>
      </div>
    </PageContainer>
  )
}

export default Home
