import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../hooks/useAuth'
import { FiCheck, FiX } from 'react-icons/fi'
import toast from 'react-hot-toast'

const plans = [
  {
    name: 'Free',
    price: '$0',
    description: 'Perfect for getting started',
    features: [
      { name: '150 Access Tokens', included: true },
      { name: 'Basic Code Generation', included: true },
      { name: 'Basic API Generation', included: true },
      { name: 'Basic Project Analysis', included: true },
      { name: 'Community Support', included: true },
      { name: 'Advanced Features', included: false },
      { name: 'Priority Support', included: false },
      { name: 'Custom Solutions', included: false },
    ],
    buttonText: 'Get Started',
    highlighted: false,
  },
  {
    name: 'Pro',
    price: '$29',
    period: '/month',
    description: 'For professional developers',
    features: [
      { name: '500 Access Tokens', included: true },
      { name: 'Advanced Code Generation', included: true },
      { name: 'Advanced API Generation', included: true },
      { name: 'Advanced Project Analysis', included: true },
      { name: 'Priority Support', included: true },
      { name: 'Advanced Features', included: true },
      { name: 'Custom Solutions', included: false },
      { name: 'Dedicated Support', included: false },
    ],
    buttonText: 'Upgrade to Pro',
    highlighted: true,
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    description: 'For large organizations',
    features: [
      { name: 'Unlimited Access Tokens', included: true },
      { name: 'Custom Code Generation', included: true },
      { name: 'Custom API Generation', included: true },
      { name: 'Custom Project Analysis', included: true },
      { name: 'Dedicated Support', included: true },
      { name: 'Advanced Features', included: true },
      { name: 'Custom Solutions', included: true },
      { name: 'SLA Guarantee', included: true },
    ],
    buttonText: 'Contact Sales',
    highlighted: false,
  },
]

export default function Pricing() {
  const { user, token } = useAuth()
  const isAuthenticated = !!token
  const navigate = useNavigate()
  const [currentPlan, setCurrentPlan] = useState('free')

  useEffect(() => {
    if (user?.subscription?.plan) {
      setCurrentPlan(user.subscription.plan)
    }
  }, [user])

  const handlePlanSelect = async (plan) => {
    if (!isAuthenticated) {
      navigate('/login')
      return
    }

    if (plan === 'enterprise') {
      // Handle enterprise contact form
      navigate('/contact')
      return
    }

    try {
      const response = await fetch('/api/users/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ plan }),
      })

      if (!response.ok) {
        throw new Error('Failed to update subscription')
      }

      const data = await response.json()
      toast.success(`Successfully upgraded to ${plan} plan!`)
      // Refresh user data or update local state
    } catch (error) {
      toast.error(error.message)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#181a20] via-[#232526] to-[#23243a] py-16 px-4 sm:px-6 lg:px-8 text-white font-sans" style={{ fontFamily: 'Inter, Space Grotesk, sans-serif' }}>
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-extrabold bg-gradient-to-r from-cyan-400 to-blue-600 text-transparent bg-clip-text drop-shadow-glow tracking-wider mb-4">Simple, transparent pricing</h2>
          <p className="mt-4 text-lg text-cyan-200/80 font-medium">Choose the plan that best fits your needs</p>
        </div>
        <div className="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-8 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:grid-cols-3">
          {plans.map((plan) => (
            <div
              key={plan.name}
              className={`rounded-3xl shadow-2xl bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 border ${plan.highlighted ? 'border-2 border-cyan-400' : 'border-cyan-900/20'} p-8 flex flex-col items-center justify-between backdrop-blur-xl hover:shadow-3xl transition-all`}
            >
              <div className="w-full text-center">
                <h3 className="text-2xl font-bold text-cyan-100 mb-2 tracking-wide">{plan.name}</h3>
                <p className="mt-2 text-cyan-200/80 font-medium">{plan.description}</p>
                <p className="mt-8">
                  <span className="text-5xl font-extrabold text-white drop-shadow-glow">{plan.price}</span>
                  {plan.period && (
                    <span className="text-base font-medium text-cyan-200/80">{plan.period}</span>
                  )}
                </p>
                <button
                  onClick={() => handlePlanSelect(plan.name.toLowerCase())}
                  className={`mt-8 w-full bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold py-3 px-6 rounded-xl shadow-lg transition-all text-lg focus:outline-none focus:ring-2 focus:ring-cyan-400 ${currentPlan === plan.name.toLowerCase() ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={currentPlan === plan.name.toLowerCase()}
                >
                  {currentPlan === plan.name.toLowerCase() ? 'Current Plan' : plan.buttonText}
                </button>
              </div>
              <div className="pt-8 pb-4 w-full">
                <h4 className="text-sm font-semibold text-cyan-100 tracking-wide uppercase mb-4">What's included</h4>
                <ul className="space-y-4">
                  {plan.features.map((feature) => (
                    <li key={feature.name} className="flex items-center gap-3">
                      {feature.included ? (
                        <FiCheck className="h-5 w-5 text-green-400 animate-bounce drop-shadow-glow" aria-hidden="true" />
                      ) : (
                        <FiX className="h-5 w-5 text-cyan-900/40" aria-hidden="true" />
                      )}
                      <span className="text-base text-cyan-200/80 font-medium">{feature.name}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
      <style>{`.drop-shadow-glow { filter: drop-shadow(0 0 6px #00fff7cc); }`}</style>
    </div>
  )
} 