# Backend Installation Guide

## 📋 **COMPLETE DEPENDENCY LIST**

### **🔧 Core Framework Dependencies**
- **FastAPI 0.104.1** - Modern web framework
- **Uvicorn 0.24.0** - ASGI server with WebSocket support
- **Python-multipart 0.0.6** - File upload support
- **Aiofiles 23.2.1** - Async file operations
- **SSE-Starlette 1.8.2** - Server-Sent Events for streaming

### **💾 Database Dependencies**
- **Motor 3.3.2** - Async MongoDB driver
- **PyMongo 4.6.0** - MongoDB Python driver

### **🔐 Authentication & Security**
- **Passlib 1.7.4** - Password hashing
- **Python-Jose 3.3.0** - JWT token handling
- **Bcrypt 4.1.2** - Password encryption
- **Python-dotenv 1.0.0** - Environment variables

### **🤖 AI & Machine Learning**
- **Llama-cpp-python 0.2.20** - Local LLM inference
- **PyTorch 2.1.0** - Deep learning framework
- **Transformers 4.35.0** - Hugging Face transformers
- **Accelerate 0.24.0** - Model acceleration
- **SentencePiece 0.1.99** - Text tokenization

### **🎨 Image Generation & Processing**
- **Pillow 10.1.0** - Image processing library
- **Aiohttp 3.9.1** - Async HTTP client for API calls
- **Requests 2.31.0** - HTTP library for external APIs

### **🐳 Container & Preview System**
- **Docker 6.1.3** - Container management for live previews

### **📦 File Processing**
- **Zipfile36 0.1.3** - Enhanced ZIP file handling

### **🔍 Code Analysis**
- **Javalang 0.13.0** - Java code parsing
- **Esprima 4.0.1** - JavaScript code parsing

### **🧪 Testing (Optional)**
- **Pytest 7.4.3** - Testing framework
- **Pytest-asyncio 0.21.1** - Async testing support
- **HTTPX 0.25.2** - Async HTTP client for testing

## 🚀 **INSTALLATION STEPS**

### **Step 1: Create Virtual Environment**
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### **Step 2: Install Dependencies**
```bash
# Install all dependencies
pip install -r requirements.txt

# Or install with specific Python version
python -m pip install -r requirements.txt
```

### **Step 3: Install Optional Dependencies**

#### **For Stable Diffusion API (Optional)**
```bash
# If using Stability AI API
pip install stability-sdk==0.8.4
```

#### **For Local Stable Diffusion (Optional)**
```bash
# If running local Automatic1111 WebUI
pip install gradio==4.7.1
```

### **Step 4: System Dependencies**

#### **For Docker Support (Recommended)**
- Install Docker Desktop from https://docker.com
- Ensure Docker is running for live preview features

#### **For Local AI Models**
- Ensure sufficient RAM (8GB+ recommended)
- GPU support optional but recommended for faster inference

## ⚙️ **CONFIGURATION**

### **Environment Variables**
Create a `.env` file in the backend directory:
```env
# Database
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=bharat_db

# JWT Authentication
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Stable Diffusion (Optional)
STABILITY_API_KEY=your-stability-ai-key
SD_API_URL=http://127.0.0.1:7860

# Model Paths
MODEL_PATH=./models/codestral-7b-v0.1.Q4_K_M.gguf
```

### **MongoDB Setup**
```bash
# Install MongoDB Community Edition
# Windows: Download from https://mongodb.com
# macOS: brew install mongodb-community
# Ubuntu: sudo apt install mongodb

# Start MongoDB service
# Windows: Start MongoDB service from Services
# macOS/Linux: sudo systemctl start mongod
```

## 🔧 **TROUBLESHOOTING**

### **Common Installation Issues**

#### **1. PyTorch Installation**
```bash
# If PyTorch fails to install, try:
pip install torch==2.1.0 --index-url https://download.pytorch.org/whl/cpu
```

#### **2. Llama-cpp-python Compilation**
```bash
# If compilation fails, install pre-built wheel:
pip install llama-cpp-python --prefer-binary
```

#### **3. Docker Permission Issues**
```bash
# On Linux, add user to docker group:
sudo usermod -aG docker $USER
# Then logout and login again
```

#### **4. Memory Issues**
- Ensure at least 8GB RAM available
- Close other applications during installation
- Consider using smaller AI models

### **Dependency Conflicts**
```bash
# If you encounter conflicts, create fresh environment:
pip uninstall -r requirements.txt -y
pip install -r requirements.txt --force-reinstall
```

## 🎯 **VERIFICATION**

### **Test Installation**
```bash
# Test basic imports
python -c "import fastapi, motor, llama_cpp, PIL; print('✅ All core dependencies installed')"

# Test optional dependencies
python -c "import docker; print('✅ Docker support available')"
```

### **Start Development Server**
```bash
# Start the backend server
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Server should start at http://localhost:8000
# API docs available at http://localhost:8000/docs
```

## 📊 **SYSTEM REQUIREMENTS**

### **Minimum Requirements**
- **Python**: 3.8+
- **RAM**: 4GB (8GB+ recommended)
- **Storage**: 5GB free space
- **OS**: Windows 10+, macOS 10.14+, Ubuntu 18.04+

### **Recommended Requirements**
- **Python**: 3.10+
- **RAM**: 16GB
- **Storage**: 20GB free space (for AI models)
- **GPU**: NVIDIA GPU with 4GB+ VRAM (optional)
- **Docker**: Latest version

## 🚀 **PRODUCTION DEPLOYMENT**

### **Additional Production Dependencies**
```bash
# For production deployment
pip install gunicorn==21.2.0
pip install nginx-python==1.1.0
```

### **Environment Setup**
```bash
# Set production environment
export ENVIRONMENT=production
export DEBUG=False
```

The backend is now ready with all enhanced features including image generation, live preview, and packaging capabilities! 🎉
