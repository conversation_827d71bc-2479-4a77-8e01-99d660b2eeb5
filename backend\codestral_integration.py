from llama_cpp import Llama
import time
from typing import Dict, List, Generator
import os
from pathlib import Path
import json
from datetime import datetime
import shutil
import uuid
import logging
import re
import asyncio

logger = logging.getLogger(__name__)

# Model paths mapping
MODEL_PATHS = {
    "vikki": "models/mistral-7b-instruct-v0.2.Q5_K_M.gguf",
    "vikki_advanced": "models/Codestral-22B-v0.1-Q4_K_M.gguf"
}

# Initialize models
models = {}

# Try to load VIKKI AI (Mistral) model
try:
    models["vikki"] = Llama(
        model_path=MODEL_PATHS["vikki"],
        n_ctx=1500,
        n_threads=8
    )
    logger.info("Successfully loaded VIKKI AI model")
except Exception as e:
    logger.error(f"Failed to load VIKKI AI model: {str(e)}")
    raise RuntimeError("Failed to load VIKKI AI model - this is required")

# Try to load VIKKI AI Advanced (Codestral) model
try:
    models["vikki_advanced"] = Llama(
        model_path=MODEL_PATHS["vikki_advanced"],
        n_ctx=1500,
        n_threads=8,
        n_gpu_layers=0  # Force CPU only to reduce memory usage
    )
    logger.info("Successfully loaded VIKKI AI Advanced model")
except Exception as e:
    logger.warning(f"Failed to load VIKKI AI Advanced model: {str(e)}")
    logger.warning("VIKKI AI Advanced features will be disabled")
    # Don't raise error, just log warning

def detect_framework_preferences(prompt: str) -> Dict[str, str]:
    """Detect user's preferred frameworks from the prompt"""
    prompt_lower = prompt.lower()

    # Frontend framework detection
    frontend = "react"  # default
    if "next.js" in prompt_lower or "nextjs" in prompt_lower:
        frontend = "nextjs"
    elif "vue" in prompt_lower or "vue.js" in prompt_lower:
        frontend = "vue"
    elif "angular" in prompt_lower:
        frontend = "angular"
    elif "svelte" in prompt_lower:
        frontend = "svelte"
    elif "vanilla" in prompt_lower or "html" in prompt_lower:
        frontend = "vanilla"

    # Backend framework detection
    backend = "fastapi"  # default
    if "django" in prompt_lower:
        backend = "django"
    elif "flask" in prompt_lower:
        backend = "flask"
    elif "express" in prompt_lower or "node" in prompt_lower:
        backend = "express"
    elif "spring" in prompt_lower or "java" in prompt_lower:
        backend = "spring"
    elif "laravel" in prompt_lower or "php" in prompt_lower:
        backend = "laravel"
    elif "rails" in prompt_lower or "ruby" in prompt_lower:
        backend = "rails"

    # Database detection
    database = "sqlite"  # default
    if "postgresql" in prompt_lower or "postgres" in prompt_lower:
        database = "postgresql"
    elif "mysql" in prompt_lower:
        database = "mysql"
    elif "mongodb" in prompt_lower or "mongo" in prompt_lower:
        database = "mongodb"
    elif "redis" in prompt_lower:
        database = "redis"

    return {
        "frontend": frontend,
        "backend": backend,
        "database": database
    }

def get_framework_structure(frameworks: Dict[str, str]) -> Dict:
    """Get the file structure and requirements for the selected frameworks"""
    structures = {
        "react": {
            "files": [
                "package.json", "vite.config.ts", "tailwind.config.js", "postcss.config.js",
                "tsconfig.json", "index.html", "src/main.tsx", "src/App.tsx", "src/index.css",
                "src/components/", "src/pages/", "src/hooks/", "src/utils/", "src/types/"
            ],
            "description": "React with TypeScript, Vite, Tailwind CSS"
        },
        "nextjs": {
            "files": [
                "package.json", "next.config.js", "tailwind.config.js", "postcss.config.js",
                "tsconfig.json", "app/layout.tsx", "app/page.tsx", "app/globals.css",
                "components/", "lib/", "types/", "hooks/", "utils/"
            ],
            "description": "Next.js 14 with App Router, TypeScript, Tailwind CSS"
        },
        "vue": {
            "files": [
                "package.json", "vite.config.ts", "tailwind.config.js", "postcss.config.js",
                "tsconfig.json", "index.html", "src/main.ts", "src/App.vue", "src/style.css",
                "src/components/", "src/views/", "src/composables/", "src/utils/", "src/types/"
            ],
            "description": "Vue 3 with TypeScript, Vite, Tailwind CSS"
        },
        "fastapi": {
            "files": [
                "main.py", "requirements.txt", "models.py", "auth.py", "database.py",
                ".env", "Dockerfile", "routers/", "schemas/", "utils/"
            ],
            "description": "FastAPI with SQLAlchemy, JWT authentication"
        },
        "django": {
            "files": [
                "manage.py", "requirements.txt", "settings.py", "urls.py", "wsgi.py",
                "models.py", "views.py", "serializers.py", "admin.py", ".env", "Dockerfile"
            ],
            "description": "Django with Django REST Framework"
        },
        "express": {
            "files": [
                "package.json", "server.js", "app.js", "routes/", "models/", "middleware/",
                "controllers/", "utils/", ".env", "Dockerfile"
            ],
            "description": "Express.js with Node.js, JWT authentication"
        }
    }

    frontend_info = structures.get(frameworks["frontend"], structures["react"])
    backend_info = structures.get(frameworks["backend"], structures["fastapi"])

    return {
        "frontend": frontend_info,
        "backend": backend_info,
        "database": frameworks["database"]
    }

def generate_code_streaming(prompt: str, model_type: str = "vikki", user_id: str = None) -> Generator[Dict, None, None]:
    """Generate code with streaming updates and live progress"""
    try:
        # Map 'basic' and 'advanced' to the correct model keys
        if model_type == "basic":
            model_type = "vikki"
        elif model_type == "advanced":
            model_type = "vikki_advanced"
        # Validate model type
        if model_type not in models:
            if model_type == "vikki_advanced":
                yield {
                    "type": "error",
                    "data": {
                        "error": "VIKKI AI Advanced model is not available. Please use VIKKI AI instead."
                    }
                }
                return
            else:
                yield {
                    "type": "error",
                    "data": {
                        "error": f"Invalid model type: {model_type}"
                    }
                }
                return

        # Detect user's framework preferences
        frameworks = detect_framework_preferences(prompt)
        structure_info = get_framework_structure(frameworks)

        project_id = str(uuid.uuid4())
        project_dir = Path("generated_projects") / project_id
        project_dir.mkdir(parents=True, exist_ok=True)

        # Generate dynamic prompt based on detected frameworks
        frontend_desc = structure_info["frontend"]["description"]
        backend_desc = structure_info["backend"]["description"]
        database = structure_info["database"]

        # Build file structure requirements
        frontend_files = "\n".join([f"- {file}" for file in structure_info["frontend"]["files"]])
        backend_files = "\n".join([f"- {file}" for file in structure_info["backend"]["files"]])

        structure_prompt = f"""<s>[INST]
Create a complete, production-ready full-stack application for: {prompt}

DETECTED PREFERENCES:
- Frontend: {frontend_desc}
- Backend: {backend_desc}
- Database: {database.upper()}
- Authentication: JWT tokens
- Complete file structure with all necessary files

MANDATORY FILES TO INCLUDE:

BACKEND (backend/ folder):
{backend_files}

FRONTEND (frontend/ folder):
{frontend_files}

ROOT:
- README.md (comprehensive setup and usage documentation)
- docker-compose.yml (if applicable)
- .gitignore (appropriate for the tech stack)
- package.json (root level if monorepo)

IMPORTANT REQUIREMENTS:
1. Generate COMPLETE, FUNCTIONAL code for each file
2. No placeholders or comments like "// Add more code here"
3. Include proper error handling and validation
4. Add comprehensive comments explaining the code
5. Ensure frontend and backend are properly connected
6. Include proper environment configuration
7. Add database models and migrations if needed
8. Include authentication and authorization
9. Add proper CORS configuration
10. Include comprehensive README with setup instructions

INTEGRATION REQUIREMENTS:
- Frontend should make API calls to backend endpoints
- Backend should serve the frontend in production
- Database should be properly configured and connected
- Authentication should work between frontend and backend
- Include proper environment variables for different environments

Return in this EXACT format:
// FILE: filepath
// CONTENT:
file content here
// END FILE

GENERATE A COMPLETE, CONNECTED, PRODUCTION-READY APPLICATION.
[/INST]</s>"""

        yield {
            "type": "status",
            "data": {
                "status": "generating",
                "message": "Generating project structure..."
            }
        }

        llm = models[model_type]
        structure = llm(structure_prompt, max_tokens=8192, temperature=0.1, stop=["</s>"])
        print("Model output:", structure)  # Debug print

        if not structure or "choices" not in structure:
            raise ValueError("Failed to generate project structure (no choices in model output)")

        if not structure["choices"] or "text" not in structure["choices"][0]:
            raise ValueError("Model output missing 'text' in choices")

        files = parse_project_files(structure["choices"][0]["text"])
        if not isinstance(files, list) or not files:
            raise ValueError("No files were generated or files list is invalid")

        total_files = len(files)
        for index, file_info in enumerate(files, 1):
            file_path = project_dir / file_info["path"]
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(file_info["content"])

            yield {
                "type": "file",
                "data": {
                    "file": file_info,
                    "progress": {
                        "current": index,
                        "total": total_files,
                        "percentage": (index / total_files) * 100
                    }
                }
            }

        project_info = {
            "id": project_id,
            "name": f"Project {project_id[:8]}",
            "description": prompt[:100] + "..." if len(prompt) > 100 else prompt,
            "created_at": datetime.utcnow().isoformat(),
            "files": [f["path"] for f in files],
            "user_id": user_id or ""
        }

        with open(project_dir / "project_info.json", "w") as f:
            json.dump(project_info, f)

        yield {
            "type": "complete",
            "data": {
                "project": {
                    "id": project_id,
                    "files": files
                }
            }
        }

    except Exception as e:
        logger.error(f"Error generating code: {str(e)}")
        yield {
            "type": "error",
            "data": {
                "error": str(e)
            }
        }

def generate_code(prompt: str, model_type: str = "vikki", user_id: str = None) -> Dict:
    """Generate full code with selected model"""
    try:
        # Map 'basic' and 'advanced' to the correct model keys
        if model_type == "basic":
            model_type = "vikki"
        elif model_type == "advanced":
            model_type = "vikki_advanced"
        # Validate model type
        if model_type not in models:
            if model_type == "vikki_advanced":
                raise ValueError("VIKKI AI Advanced model is not available. Please use VIKKI AI instead.")
            else:
                raise ValueError(f"Invalid model type: {model_type}")

        # Detect user's framework preferences
        frameworks = detect_framework_preferences(prompt)
        structure_info = get_framework_structure(frameworks)

        project_id = str(uuid.uuid4())
        project_dir = Path("generated_projects") / project_id
        project_dir.mkdir(parents=True, exist_ok=True)

        # Generate dynamic prompt based on detected frameworks
        frontend_desc = structure_info["frontend"]["description"]
        backend_desc = structure_info["backend"]["description"]
        database = structure_info["database"]

        # Build file structure requirements
        frontend_files = "\n".join([f"- {file}" for file in structure_info["frontend"]["files"]])
        backend_files = "\n".join([f"- {file}" for file in structure_info["backend"]["files"]])

        structure_prompt = f"""<s>[INST]
Create a complete, production-ready full-stack application for: {prompt}

DETECTED PREFERENCES:
- Frontend: {frontend_desc}
- Backend: {backend_desc}
- Database: {database.upper()}
- Authentication: JWT tokens
- Complete file structure with all necessary files

MANDATORY FILES TO INCLUDE:

BACKEND (backend/ folder):
{backend_files}

FRONTEND (frontend/ folder):
{frontend_files}

ROOT:
- README.md (comprehensive setup and usage documentation)
- docker-compose.yml (if applicable)
- .gitignore (appropriate for the tech stack)
- package.json (root level if monorepo)

IMPORTANT REQUIREMENTS:
1. Generate COMPLETE, FUNCTIONAL code for each file
2. No placeholders or comments like "// Add more code here"
3. Include proper error handling and validation
4. Add comprehensive comments explaining the code
5. Ensure frontend and backend are properly connected
6. Include proper environment configuration
7. Add database models and migrations if needed
8. Include authentication and authorization
9. Add proper CORS configuration
10. Include comprehensive README with setup instructions

INTEGRATION REQUIREMENTS:
- Frontend should make API calls to backend endpoints
- Backend should serve the frontend in production
- Database should be properly configured and connected
- Authentication should work between frontend and backend
- Include proper environment variables for different environments

Return in this EXACT format:
// FILE: filepath
// CONTENT:
file content here
// END FILE

GENERATE A COMPLETE, CONNECTED, PRODUCTION-READY APPLICATION.
[/INST]</s>"""

        llm = models[model_type]
        structure = llm(structure_prompt, max_tokens=8192, temperature=0.1, stop=["</s>"])
        if not structure or "choices" not in structure:
            raise ValueError("Failed to generate structure")

        files = parse_project_files(structure["choices"][0]["text"])
        if not files:
            raise ValueError("No files generated")

        for file_info in files:
            file_path = project_dir / file_info["path"]
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(file_info["content"])

        project_info = {
            "id": project_id,
            "name": f"Project {project_id[:8]}",
            "description": prompt[:100] + "..." if len(prompt) > 100 else prompt,
            "created_at": datetime.utcnow().isoformat(),
            "files": [f["path"] for f in files],
            "user_id": user_id or ""
        }

        with open(project_dir / "project_info.json", "w") as f:
            json.dump(project_info, f)

        return {"project": {"id": project_id, "files": files}}

    except Exception as e:
        logger.error(f"Error generating code: {str(e)}")
        raise

def parse_project_files(generated_text: str) -> List[Dict]:
    """Parse AI-generated text into file objects"""
    files = []
    current_file = None
    current_content = []
    in_content = False

    lines = generated_text.splitlines()
    for line in lines:
        line_stripped = line.strip()

        if line_stripped.startswith("// FILE:"):
            # Save previous file if exists
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_content = []

            # Start new file
            filepath = line_stripped.replace("// FILE:", "").strip()
            current_file = {"path": filepath, "content": ""}
            in_content = False

        elif line_stripped.startswith("// CONTENT:"):
            current_content = []
            in_content = True

        elif line_stripped.startswith("// END FILE"):
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_file = None
                current_content = []
            in_content = False

        elif in_content and current_file is not None:
            # Preserve original line formatting (don't strip)
            current_content.append(line)

        # Also handle code blocks format ```filepath
        elif line_stripped.startswith("```") and not line_stripped == "```":
            # Save previous file if exists
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_content = []

            # Start new file from code block
            filepath = line_stripped.replace("```", "").strip()
            if filepath:  # Only if there's a filepath
                current_file = {"path": filepath, "content": ""}
                in_content = True

        elif line_stripped == "```" and current_file:
            # End of code block
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_file = None
                current_content = []
            in_content = False

        elif in_content and current_file is not None and not line_stripped.startswith("//"):
            # Add content line (preserve formatting)
            current_content.append(line)

    # Handle last file
    if current_file and current_content:
        current_file["content"] = "\n".join(current_content)
        files.append(current_file)

    return files

def generate_single_file_code(prompt: str, model_type: str = "vikki") -> str:
    """Generate a single file's code"""
    try:
        # Validate model type
        if model_type not in models:
            if model_type == "vikki_advanced":
                raise ValueError("VIKKI AI Advanced model is not available. Please use VIKKI AI instead.")
            else:
                raise ValueError(f"Invalid model type: {model_type}")

        file_prompt = f"""<s>[INST]
Generate code for: {prompt}
Return ONLY the code, no explanations.
[/INST]</s>"""

        llm = models[model_type]
        output = llm(file_prompt, max_tokens=2048, temperature=0.1, stop=["</s>"])
        return output["choices"][0]["text"].strip()
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        raise
