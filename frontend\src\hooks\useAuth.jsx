import { useState, useEffect, createContext, useContext } from 'react';
import axios from '../config/axios';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(() => {
    // Check localStorage first (for remember me), then sessionStorage
    return localStorage.getItem('token') || sessionStorage.getItem('token');
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUser = async () => {
      if (!token) {
        setLoading(false);
        return;
      }
      try {
        const response = await axios.get('/api/users/me', {
          headers: { Authorization: `Bearer ${token}` }
        });
        setUser(response.data);
        setError(null);
      } catch (err) {
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
        setToken(null);
        setUser(null);
        setError(null);
      } finally {
        setLoading(false);
      }
    };
    fetchUser();
  }, [token]);

  const login = async (username, password, rememberMe = false) => {
    try {
      // Use the new enhanced login endpoint
      const response = await axios.post('/api/login', {
        username,
        password,
        remember_me: rememberMe
      });

      const { access_token: newToken, expires_in, remember_me } = response.data;

      // Store token with appropriate storage method
      if (remember_me) {
        localStorage.setItem('token', newToken);
        localStorage.setItem('remember_me', 'true');
      } else {
        sessionStorage.setItem('token', newToken);
        localStorage.removeItem('remember_me');
      }

      setToken(newToken);

      // Fetch user data after successful login
      const userResponse = await axios.get('/api/users/me', {
        headers: { Authorization: `Bearer ${newToken}` }
      });
      setUser(userResponse.data);
      setError(null);
      toast.success('Login successful!');
      navigate('/dashboard');
      return true;
    } catch (err) {
      setError(err.response?.data?.detail || 'Login failed');
      toast.error(err.response?.data?.detail || 'Login failed');
      return false;
    }
  };

  const forgotPassword = async (email) => {
    try {
      await axios.post('/api/forgot-password', { email });
      toast.success('If the email exists, a password reset link has been sent.');
      return true;
    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to send reset email');
      toast.error(err.response?.data?.detail || 'Failed to send reset email');
      return false;
    }
  };

  const resetPassword = async (token, newPassword) => {
    try {
      await axios.post('/api/reset-password', {
        token,
        new_password: newPassword
      });
      toast.success('Password has been successfully reset. Please login with your new password.');
      navigate('/login');
      return true;
    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to reset password');
      toast.error(err.response?.data?.detail || 'Failed to reset password');
      return false;
    }
  };

  const register = async (username, email, password, fullName) => {
    try {
      const userData = {
        username,
        email,
        password,
        full_name: fullName
      };
      await axios.post('/api/register', userData);
      toast.success('Registration successful! Please login.');
      navigate('/login');
      setError(null);
      return true;
    } catch (err) {
      setError(err.response?.data?.detail || 'Registration failed');
      toast.error(err.response?.data?.detail || 'Registration failed');
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('remember_me');
    sessionStorage.removeItem('token');
    setToken(null);
    setUser(null);
    setError(null);
    navigate('/login');
    toast.success('Logged out successfully');
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await axios.put('/api/users/me', profileData);
      setUser(response.data);
      setError(null);
      toast.success('Profile updated successfully');
      return true;
    } catch (err) {
      setError(err.response?.data?.detail || 'Profile update failed');
      toast.error(err.response?.data?.detail || 'Profile update failed');
      return false;
    }
  };

  const value = {
    user,
    token,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    forgotPassword,
    resetPassword
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 