import { useState, useEffect } from 'react';
import axios from '../config/axios';
import { useAuth } from './useAuth';
import toast from 'react-hot-toast';

export const useTokens = () => {
  const { token } = useAuth();
  const [tokenInfo, setTokenInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchTokenInfo = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/tokens/info');
      setTokenInfo(response.data);
      setError(null);
    } catch (err) {
      console.error('Token fetch error:', err);
      setError(err.response?.data?.detail || 'Failed to fetch token info');
      // Don't show toast for every fetch error to avoid spam
    } finally {
      setLoading(false);
    }
  };

  const consumeTokens = async (feature, tokensUsed, description = '') => {
    try {
      const response = await axios.post('/api/tokens/use', {
        feature,
        tokens_used: tokensUsed,
        description
      });

      // Refresh token info after using tokens
      await fetchTokenInfo();

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || 'Failed to use tokens';
      toast.error(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const refreshTokens = async () => {
    try {
      const response = await axios.post('/api/tokens/refresh');

      // Refresh token info after refreshing tokens
      await fetchTokenInfo();

      toast.success('Tokens refreshed successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || 'Failed to refresh tokens';
      toast.error(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateSubscription = async (plan) => {
    try {
      const response = await axios.post('/api/users/subscription', { plan });

      // Refresh token info after subscription update
      await fetchTokenInfo();

      toast.success('Subscription updated successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || 'Failed to update subscription';
      toast.error(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    if (token) {
      fetchTokenInfo();
    } else {
      setTokenInfo(null);
      setLoading(false);
    }
  }, [token]);

  return {
    tokenInfo,
    loading,
    error,
    consumeTokens,
    refreshTokens,
    fetchTokenInfo,
    refreshTokenInfo: fetchTokenInfo, // Add alias for backward compatibility
    updateSubscription
  };
}; 