import React from 'react';

const Logo = ({ className = "h-8 w-8", textClassName = "text-xl font-bold" }) => {
  return (
    <div className="flex items-center gap-3">
      {/* SVG Logo */}
      <div className={`${className} bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg`}>
        <svg
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-5 h-5 text-white"
        >
          {/* V shape for VIKKI */}
          <path
            d="M3 4L9 16L12 10L15 16L21 4"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          {/* AI circuit pattern */}
          <circle
            cx="12"
            cy="8"
            r="1.5"
            fill="currentColor"
          />
          <circle
            cx="8"
            cy="12"
            r="1"
            fill="currentColor"
            opacity="0.7"
          />
          <circle
            cx="16"
            cy="12"
            r="1"
            fill="currentColor"
            opacity="0.7"
          />
          {/* Connection lines */}
          <path
            d="M8 12L12 8L16 12"
            stroke="currentColor"
            strokeWidth="1"
            opacity="0.5"
          />
        </svg>
      </div>
      
      {/* Text Logo */}
      <span className={`${textClassName} bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent`}>
        VIKKI AI
      </span>
    </div>
  );
};

export default Logo;
