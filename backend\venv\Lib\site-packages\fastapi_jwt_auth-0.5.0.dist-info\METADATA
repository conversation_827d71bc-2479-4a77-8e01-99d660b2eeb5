Metadata-Version: 2.1
Name: fastapi-jwt-auth
Version: 0.5.0
Summary: FastAPI extension that provides JWT Auth support (secure, easy to use and lightweight)
Home-page: https://github.com/IndominusByte/fastapi-jwt-auth
License: UNKNOWN
Author: <PERSON><PERSON><PERSON><PERSON>
Author-email: <EMAIL>
Requires-Python: >=3.6
Description-Content-Type: text/markdown
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Dist: fastapi>=0.61.0
Requires-Dist: PyJWT>=1.7.1,<2.0.0
Requires-Dist: cryptography>=2.6,<4.0.0 ; extra == "asymmetric"
Requires-Dist: cryptography>=2.6,<4.0.0 ; extra == "dev"
Requires-Dist: uvicorn>=0.11.5,<0.12.0 ; extra == "dev"
Requires-Dist: mkdocs>=1.1.2,<2.0.0 ; extra == "doc"
Requires-Dist: mkdocs-material>=5.5.0,<6.0.0 ; extra == "doc"
Requires-Dist: markdown-include>=0.5.1,<0.6.0 ; extra == "doc"
Requires-Dist: pytest==6.0.1 ; extra == "test"
Requires-Dist: pytest-cov==2.10.0 ; extra == "test"
Requires-Dist: coveralls==2.1.2 ; extra == "test"
Project-URL: Documentation, https://indominusbyte.github.io/fastapi-jwt-auth/
Provides-Extra: asymmetric
Provides-Extra: dev
Provides-Extra: doc
Provides-Extra: test

<h1 align="left" style="margin-bottom: 20px; font-weight: 500; font-size: 50px; color: black;">
  FastAPI JWT Auth
</h1>

![Tests](https://github.com/IndominusByte/fastapi-jwt-auth/workflows/Tests/badge.svg)
[![Coverage Status](https://coveralls.io/repos/github/IndominusByte/fastapi-jwt-auth/badge.svg?branch=master)](https://coveralls.io/github/IndominusByte/fastapi-jwt-auth?branch=master)
[![PyPI version](https://badge.fury.io/py/fastapi-jwt-auth.svg)](https://badge.fury.io/py/fastapi-jwt-auth)
[![Downloads](https://static.pepy.tech/personalized-badge/fastapi-jwt-auth?period=total&units=international_system&left_color=grey&right_color=brightgreen&left_text=Downloads)](https://pepy.tech/project/fastapi-jwt-auth)

---

**Documentation**: <a href="https://indominusbyte.github.io/fastapi-jwt-auth" target="_blank">https://indominusbyte.github.io/fastapi-jwt-auth</a>

**Source Code**: <a href="https://github.com/IndominusByte/fastapi-jwt-auth" target="_blank">https://github.com/IndominusByte/fastapi-jwt-auth</a>

---

## Features
FastAPI extension that provides JWT Auth support (secure, easy to use and lightweight), if you were familiar with flask-jwt-extended this extension suitable for you, cause this extension inspired by flask-jwt-extended 😀

- Access tokens and refresh tokens
- Freshness Tokens
- Revoking Tokens
- Support for WebSocket authorization
- Support for adding custom claims to JSON Web Tokens
- Storing tokens in cookies and CSRF protection

## Installation
The easiest way to start working with this extension with pip

```bash
pip install fastapi-jwt-auth
```

If you want to use asymmetric (public/private) key signing algorithms, include the <b>asymmetric</b> extra requirements.
```bash
pip install 'fastapi-jwt-auth[asymmetric]'
```

## License
This project is licensed under the terms of the MIT license.

