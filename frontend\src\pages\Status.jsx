import { useState, useEffect } from 'react'
import { FiCheck, FiX, FiClock, FiActivity } from 'react-icons/fi'
import PageContainer from '../components/layout/PageContainer'

export default function Status() {
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState(new Date())

  // Mock service status data
  const mockServices = [
    {
      name: 'API Gateway',
      status: 'operational',
      description: 'Main API endpoint for all services',
      uptime: '99.9%',
      responseTime: '120ms'
    },
    {
      name: 'Code Generation',
      status: 'operational',
      description: 'AI-powered code generation service',
      uptime: '99.8%',
      responseTime: '2.3s'
    },
    {
      name: 'Project Analysis',
      status: 'operational',
      description: 'Code analysis and improvement suggestions',
      uptime: '99.7%',
      responseTime: '1.8s'
    },
    {
      name: 'Authentication',
      status: 'operational',
      description: 'User authentication and authorization',
      uptime: '99.9%',
      responseTime: '95ms'
    },
    {
      name: 'Database',
      status: 'operational',
      description: 'Primary database cluster',
      uptime: '99.9%',
      responseTime: '45ms'
    },
    {
      name: 'File Storage',
      status: 'operational',
      description: 'Project file storage and delivery',
      uptime: '99.8%',
      responseTime: '180ms'
    }
  ]

  useEffect(() => {
    // Simulate API call
    const fetchStatus = async () => {
      setLoading(true)
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      setServices(mockServices)
      setLastUpdated(new Date())
      setLoading(false)
    }

    fetchStatus()
    
    // Update every 30 seconds
    const interval = setInterval(fetchStatus, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status) => {
    switch (status) {
      case 'operational':
        return <FiCheck className="w-5 h-5 text-green-400" />
      case 'degraded':
        return <FiClock className="w-5 h-5 text-yellow-400" />
      case 'down':
        return <FiX className="w-5 h-5 text-red-400" />
      default:
        return <FiActivity className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'operational':
        return 'text-green-400'
      case 'degraded':
        return 'text-yellow-400'
      case 'down':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case 'operational':
        return 'bg-green-400/20 text-green-400 border-green-400/30'
      case 'degraded':
        return 'bg-yellow-400/20 text-yellow-400 border-yellow-400/30'
      case 'down':
        return 'bg-red-400/20 text-red-400 border-red-400/30'
      default:
        return 'bg-gray-400/20 text-gray-400 border-gray-400/30'
    }
  }

  const overallStatus = services.every(s => s.status === 'operational') ? 'operational' : 
                       services.some(s => s.status === 'down') ? 'degraded' : 'degraded'

  return (
    <PageContainer>
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] py-32 px-4 sm:px-6 lg:px-8 text-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent mb-6">
              System Status
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Real-time status of VIKKI AI services and infrastructure
            </p>
          </div>

          {/* Overall Status */}
          <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-3xl p-8 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {getStatusIcon(overallStatus)}
                <div>
                  <h2 className="text-2xl font-bold text-white">All Systems</h2>
                  <p className={`text-lg font-semibold ${getStatusColor(overallStatus)}`}>
                    {overallStatus === 'operational' ? 'Operational' : 'Degraded Performance'}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-400">Last updated</p>
                <p className="text-white font-mono">
                  {lastUpdated.toLocaleTimeString()}
                </p>
              </div>
            </div>
          </div>

          {/* Services Status */}
          <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-3xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6">Service Status</h2>
            
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-cyan-400"></div>
                <span className="ml-3 text-gray-300">Loading status...</span>
              </div>
            ) : (
              <div className="space-y-4">
                {services.map((service, index) => (
                  <div key={index} className="bg-gray-800/30 rounded-xl p-6 border border-gray-700/30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        {getStatusIcon(service.status)}
                        <div>
                          <h3 className="text-lg font-semibold text-white">{service.name}</h3>
                          <p className="text-gray-400 text-sm">{service.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-6">
                        <div className="text-right">
                          <p className="text-sm text-gray-400">Uptime</p>
                          <p className="text-white font-mono">{service.uptime}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-400">Response Time</p>
                          <p className="text-white font-mono">{service.responseTime}</p>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getStatusBadge(service.status)}`}>
                          {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Additional Information */}
          <div className="mt-8 bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-3xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6">Status Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-cyan-400 mb-3">Status Definitions</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <FiCheck className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300"><strong>Operational:</strong> Service is running normally</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FiClock className="w-4 h-4 text-yellow-400" />
                    <span className="text-gray-300"><strong>Degraded:</strong> Service is experiencing issues</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FiX className="w-4 h-4 text-red-400" />
                    <span className="text-gray-300"><strong>Down:</strong> Service is unavailable</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-cyan-400 mb-3">Need Help?</h3>
                <p className="text-gray-300 text-sm mb-3">
                  If you're experiencing issues not reflected here, please contact our support team.
                </p>
                <a 
                  href="/contact" 
                  className="inline-flex items-center text-cyan-400 hover:text-cyan-300 transition-colors text-sm"
                >
                  Contact Support →
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  )
}
