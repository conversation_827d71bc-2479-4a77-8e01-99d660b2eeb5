"""
Razorpay Payment Integration for VIKKI AI
"""
import os
import razorpay
import hmac
import hashlib
import json
from datetime import datetime, timedelta
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)

class PaymentService:
    def __init__(self):
        self.razorpay_key_id = os.getenv("RAZORPAY_KEY_ID")
        self.razorpay_key_secret = os.getenv("RAZORPAY_KEY_SECRET")
        self.webhook_secret = os.getenv("RAZORPAY_WEBHOOK_SECRET")
        self.currency = os.getenv("CURRENCY", "USD")
        
        if not self.razorpay_key_id or not self.razorpay_key_secret:
            logger.warning("Razorpay credentials not configured")
            self.client = None
        else:
            self.client = razorpay.Client(auth=(self.razorpay_key_id, self.razorpay_key_secret))
    
    def create_order(self, amount: float, plan_name: str, user_id: str) -> Dict:
        """Create a Razorpay order for subscription"""
        if not self.client:
            raise Exception("Razor<PERSON>y not configured")
        
        try:
            # Convert amount to paise (<PERSON><PERSON><PERSON><PERSON> uses smallest currency unit)
            amount_paise = int(amount * 100)
            
            order_data = {
                "amount": amount_paise,
                "currency": self.currency,
                "receipt": f"order_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "notes": {
                    "plan_name": plan_name,
                    "user_id": user_id,
                    "created_at": datetime.now().isoformat()
                }
            }
            
            order = self.client.order.create(data=order_data)
            
            return {
                "order_id": order["id"],
                "amount": amount,
                "currency": self.currency,
                "key_id": self.razorpay_key_id,
                "plan_name": plan_name,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error creating Razorpay order: {e}")
            raise Exception(f"Failed to create payment order: {str(e)}")
    
    def verify_payment(self, payment_data: Dict) -> bool:
        """Verify Razorpay payment signature"""
        try:
            razorpay_order_id = payment_data.get("razorpay_order_id")
            razorpay_payment_id = payment_data.get("razorpay_payment_id")
            razorpay_signature = payment_data.get("razorpay_signature")
            
            if not all([razorpay_order_id, razorpay_payment_id, razorpay_signature]):
                return False
            
            # Create signature verification string
            verification_string = f"{razorpay_order_id}|{razorpay_payment_id}"
            
            # Generate expected signature
            expected_signature = hmac.new(
                self.razorpay_key_secret.encode(),
                verification_string.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, razorpay_signature)
            
        except Exception as e:
            logger.error(f"Error verifying payment: {e}")
            return False
    
    def verify_webhook(self, payload: str, signature: str) -> bool:
        """Verify Razorpay webhook signature"""
        try:
            if not self.webhook_secret:
                logger.warning("Webhook secret not configured")
                return False
            
            expected_signature = hmac.new(
                self.webhook_secret.encode(),
                payload.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, signature)
            
        except Exception as e:
            logger.error(f"Error verifying webhook: {e}")
            return False
    
    def get_payment_details(self, payment_id: str) -> Optional[Dict]:
        """Get payment details from Razorpay"""
        if not self.client:
            return None
        
        try:
            payment = self.client.payment.fetch(payment_id)
            return payment
        except Exception as e:
            logger.error(f"Error fetching payment details: {e}")
            return None

# Plan configurations
PLAN_CONFIGS = {
    "free": {
        "name": "Free Plan",
        "price": 0,
        "currency": "USD",
        "tokens": 150,
        "features": ["Basic code generation", "150 tokens/day", "Community support", "Token repurchase available"]
    },
    "pro": {
        "name": "Pro Plan",
        "price": 30,  # $30 per month
        "currency": "USD",
        "tokens": 500,
        "features": [
            "Advanced code generation",
            "500 tokens/day",
            "Priority support",
            "Image generation",
            "Live preview",
            "API access",
            "Token repurchase available"
        ]
    },
    "enterprise": {
        "name": "Enterprise Plan",
        "price": 299,  # $299 per user/month
        "currency": "USD",
        "tokens": 1000,
        "features": [
            "Unlimited code generation",
            "1000 tokens/day per user",
            "Dedicated support",
            "Custom integrations",
            "SLA guarantees",
            "Team collaboration",
            "Advanced analytics",
            "Token repurchase available"
        ]
    }
}

# Token repurchase options
TOKEN_PACKAGES = {
    "small": {
        "name": "100 Tokens",
        "tokens": 100,
        "price": 5,
        "currency": "USD"
    },
    "medium": {
        "name": "250 Tokens",
        "tokens": 250,
        "price": 10,
        "currency": "USD"
    },
    "large": {
        "name": "500 Tokens",
        "tokens": 500,
        "price": 18,
        "currency": "USD"
    }
}

def get_plan_config(plan_name: str) -> Optional[Dict]:
    """Get plan configuration by name"""
    return PLAN_CONFIGS.get(plan_name.lower())

def calculate_plan_price(plan_name: str, billing_cycle: str = "monthly") -> float:
    """Calculate plan price based on billing cycle"""
    plan = get_plan_config(plan_name)
    if not plan:
        return 0
    
    base_price = plan["price"]
    
    if billing_cycle == "yearly":
        # 20% discount for yearly billing
        return base_price * 12 * 0.8
    
    return base_price

async def upgrade_user_plan(db, user_id: str, plan_name: str, payment_id: str) -> bool:
    """Upgrade user plan after successful payment"""
    try:
        plan_config = get_plan_config(plan_name)
        if not plan_config:
            return False
        
        # Calculate expiry date (30 days from now)
        expiry_date = datetime.now() + timedelta(days=30)
        
        # Update user subscription
        result = await db.users.update_one(
            {"username": user_id},
            {
                "$set": {
                    "subscription": {
                        "plan": plan_name,
                        "status": "active",
                        "started_at": datetime.now(),
                        "expires_at": expiry_date,
                        "payment_id": payment_id,
                        "auto_renew": True
                    },
                    "updated_at": datetime.now()
                }
            }
        )
        
        # Update token allocation
        await db.tokens.update_one(
            {"user_id": user_id},
            {
                "$set": {
                    "total_tokens": plan_config["tokens"],
                    "used_tokens": 0,
                    "plan": plan_name,
                    "last_refresh": datetime.now(),
                    "updated_at": datetime.now()
                }
            },
            upsert=True
        )
        
        # Log payment
        await db.payments.insert_one({
            "user_id": user_id,
            "plan": plan_name,
            "amount": plan_config["price"],
            "currency": "USD",
            "payment_id": payment_id,
            "status": "completed",
            "created_at": datetime.now()
        })
        
        return result.modified_count > 0
        
    except Exception as e:
        logger.error(f"Error upgrading user plan: {e}")
        return False

async def cancel_user_subscription(db, user_id: str) -> bool:
    """Cancel user subscription"""
    try:
        result = await db.users.update_one(
            {"username": user_id},
            {
                "$set": {
                    "subscription.status": "cancelled",
                    "subscription.cancelled_at": datetime.now(),
                    "subscription.auto_renew": False,
                    "updated_at": datetime.now()
                }
            }
        )
        
        return result.modified_count > 0
        
    except Exception as e:
        logger.error(f"Error cancelling subscription: {e}")
        return False
