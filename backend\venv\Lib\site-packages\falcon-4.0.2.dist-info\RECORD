../../Scripts/falcon-bench.exe,sha256=_0FRDwUevRTaTSbhTwAtdeS13syIVBE9tz26Z1xLFMM,107913
../../Scripts/falcon-inspect-app.exe,sha256=VlJFSC9b4kH4iFtPB3WVA4TOWdUhoqDJBLZ6TfR4j7I,107919
../../Scripts/falcon-print-routes.exe,sha256=DMzV_gc-Obw9illC9emujRBX8gvTdhTbmtkgYFtyx4s,107931
falcon-4.0.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
falcon-4.0.2.dist-info/LICENSE,sha256=Pd-b5cKP4n2tFDpdx27qJSIq0d1ok0oEcGTlbtL6QMU,11560
falcon-4.0.2.dist-info/METADATA,sha256=5CGvDM3K-oqlw1PgUyuRVvh4EByN2Iz4ZHUjxUKhQYg,38430
falcon-4.0.2.dist-info/RECORD,,
falcon-4.0.2.dist-info/WHEEL,sha256=09_eAv2LFHDbyhcOULd5e3WJrC_F5q7AlLDftiw-PyE,101
falcon-4.0.2.dist-info/entry_points.txt,sha256=PnTTFQgceDibu85nzyiUqAxAHeGZQr_xvCaixpFDFv4,160
falcon-4.0.2.dist-info/top_level.txt,sha256=vNYwlU7fXo_ItV5-F6Mxhce2ncndoZQzoPQ6FqMpfpk,7
falcon/__init__.py,sha256=LUIDZ6B-U-yY-GweVAM53xXwAg-DoRXVH6_EtJCjoL4,21895
falcon/__pycache__/__init__.cpython-310.pyc,,
falcon/__pycache__/_typing.cpython-310.pyc,,
falcon/__pycache__/app.cpython-310.pyc,,
falcon/__pycache__/app_helpers.cpython-310.pyc,,
falcon/__pycache__/asgi_spec.cpython-310.pyc,,
falcon/__pycache__/constants.cpython-310.pyc,,
falcon/__pycache__/errors.cpython-310.pyc,,
falcon/__pycache__/forwarded.cpython-310.pyc,,
falcon/__pycache__/hooks.cpython-310.pyc,,
falcon/__pycache__/http_error.cpython-310.pyc,,
falcon/__pycache__/http_status.cpython-310.pyc,,
falcon/__pycache__/inspect.cpython-310.pyc,,
falcon/__pycache__/middleware.cpython-310.pyc,,
falcon/__pycache__/redirects.cpython-310.pyc,,
falcon/__pycache__/request.cpython-310.pyc,,
falcon/__pycache__/request_helpers.cpython-310.pyc,,
falcon/__pycache__/responders.cpython-310.pyc,,
falcon/__pycache__/response.cpython-310.pyc,,
falcon/__pycache__/response_helpers.cpython-310.pyc,,
falcon/__pycache__/status_codes.cpython-310.pyc,,
falcon/__pycache__/stream.cpython-310.pyc,,
falcon/__pycache__/typing.cpython-310.pyc,,
falcon/__pycache__/uri.cpython-310.pyc,,
falcon/__pycache__/version.cpython-310.pyc,,
falcon/_typing.cp310-win_amd64.pyd,sha256=wH7ULiiaMAyIrLTo9Usot41xLaaf4ieYpi9LhVyBrjI,122368
falcon/_typing.py,sha256=LQV6DjAjGGEssT2M6fHNN76ZTOrunqxzSP2CoKlFrIA,5621
falcon/app.cp310-win_amd64.pyd,sha256=MAU5EVseLpklaPAaXkeco-mfzYw4Mcky0hJTVewr4ug,220160
falcon/app.py,sha256=YZuSECYAry6kTxl84JYQGko7M0Q2cWhUr1Tl9ZtSiMI,55151
falcon/app_helpers.cp310-win_amd64.pyd,sha256=uUmC8PuZNs_q-vafIuIqGMgUdV_V--RDjoYb5wi5xUo,123904
falcon/app_helpers.py,sha256=GOQx8GiqWkdV5A6u_rEI9XP8ONeCUwgQUUumUQCC4k8,15533
falcon/asgi/__init__.py,sha256=HD0m2Bjpv3GEfuaObqmDUg_nD6cTibS5VcqLIKo5SKY,1395
falcon/asgi/__pycache__/__init__.cpython-310.pyc,,
falcon/asgi/__pycache__/_asgi_helpers.cpython-310.pyc,,
falcon/asgi/__pycache__/_request_helpers.cpython-310.pyc,,
falcon/asgi/__pycache__/app.cpython-310.pyc,,
falcon/asgi/__pycache__/multipart.cpython-310.pyc,,
falcon/asgi/__pycache__/reader.cpython-310.pyc,,
falcon/asgi/__pycache__/request.cpython-310.pyc,,
falcon/asgi/__pycache__/response.cpython-310.pyc,,
falcon/asgi/__pycache__/stream.cpython-310.pyc,,
falcon/asgi/__pycache__/structures.cpython-310.pyc,,
falcon/asgi/__pycache__/ws.cpython-310.pyc,,
falcon/asgi/_asgi_helpers.py,sha256=kVS_Y22Apg_j4bQP7wtLD5VFFg13wYD5SO2FencQLtg,3854
falcon/asgi/_request_helpers.py,sha256=af8HKs7CVV78ZLBDlvcOtoA7vAWXu5voSunA74E4zu4,1545
falcon/asgi/app.py,sha256=OvD-VJyF6H5R57CcoINe2Jb-lxoISOhiTQmbWYmy0ZA,57442
falcon/asgi/multipart.py,sha256=bj225SsEiefkhsIQCfYV-M83Z1AqJh5-g4uZeh-Usls,11084
falcon/asgi/reader.py,sha256=Y9psimNdtPZhDlMZPv9D0AFbldRWTSxH0YEiWuK-A60,12985
falcon/asgi/request.py,sha256=im96TCHfaZRU-t4GVQaxez_X6XU6dJOX4SmtfvHPqpg,36423
falcon/asgi/response.py,sha256=RaWii2TiKB3UOXQvvvcOYbEfljergROT0WvS44uqMM8,16376
falcon/asgi/stream.py,sha256=m7gN6YmA8tToUPpVOsL-aA8FMyW-xxQlGArg4arYqDY,18410
falcon/asgi/structures.py,sha256=bM0BxzvYf24Mid_jmGfwH_lziylzgscQVxiJiqLV6e8,6880
falcon/asgi/ws.py,sha256=u-Hm7w3nkOEsfPEAI-EPrkXkwjR89B6esCsmRcDUCpU,31898
falcon/asgi_spec.cp310-win_amd64.pyd,sha256=mFSxrOx6WfjoWgyyJOVE3mcnsQ4tBSUE2llncf5D8kw,33792
falcon/asgi_spec.py,sha256=mO8UB7EX-cpDszudY8NGCMBagQ8uVz6E9Bfv7AG3vcQ,2104
falcon/bench/__init__.py,sha256=B721zX4wpeQ0x3qsm22_m_vDVQ2hPr9xOu-ldOsQud8,73
falcon/bench/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/__pycache__/bench.cpython-310.pyc,,
falcon/bench/__pycache__/create.cpython-310.pyc,,
falcon/bench/bench.py,sha256=vQFU1xGCuyB5HHOyeoPQGzxDVGzKoqwAf9_n4utbeQw,11769
falcon/bench/create.py,sha256=vXygXhRSS9-x6e4AnGX3TQSLmbxcTSflR2DXJs1iWRY,3429
falcon/bench/dj/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/bench/dj/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/dj/__pycache__/manage.cpython-310.pyc,,
falcon/bench/dj/dj/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/bench/dj/dj/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/dj/dj/__pycache__/settings.cpython-310.pyc,,
falcon/bench/dj/dj/__pycache__/urls.cpython-310.pyc,,
falcon/bench/dj/dj/__pycache__/wsgi.cpython-310.pyc,,
falcon/bench/dj/dj/settings.py,sha256=uSXWaihrotpx34EOkjtJad_8r6ekMrAbYWYgkbnGo4E,3282
falcon/bench/dj/dj/urls.py,sha256=b7zeouhrKDPu0W8tsa8s3Hbg1G3GBfA-8o6MEjjqRic,141
falcon/bench/dj/dj/wsgi.py,sha256=VnW3u62K_BdwkC2jn9rUyEfoqWVYom3p7Uai9UWpTS0,396
falcon/bench/dj/hello/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/bench/dj/hello/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/dj/hello/__pycache__/admin.cpython-310.pyc,,
falcon/bench/dj/hello/__pycache__/apps.cpython-310.pyc,,
falcon/bench/dj/hello/__pycache__/models.cpython-310.pyc,,
falcon/bench/dj/hello/__pycache__/tests.cpython-310.pyc,,
falcon/bench/dj/hello/__pycache__/views.cpython-310.pyc,,
falcon/bench/dj/hello/admin.py,sha256=DPhwvkV1V_89lBTioUXcIqUk5aTHKaXSdjIHOBOfXVk,68
falcon/bench/dj/hello/apps.py,sha256=a_yBnnmIH46H2msk5GPPPMusIDgZf2bVK61IQv58zsA,90
falcon/bench/dj/hello/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/bench/dj/hello/migrations/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/dj/hello/models.py,sha256=cZwdS9dQOFqE3YLZzEvxMvIcb0YeY4yaQ6fMTKWB_AI,62
falcon/bench/dj/hello/tests.py,sha256=GgiNZG_IVvwzrZUxlYDLLalGlwRdVzC5g8rZfYqTXlg,65
falcon/bench/dj/hello/views.py,sha256=sAsfc15zsxGW4lF9BxBNGCIekAIb8Zzt5MLPMr6J868,399
falcon/bench/dj/manage.py,sha256=P6dQxOELzNNcH7yHnts7zPm7-Tq0YicmACG2GWGrqVU,830
falcon/bench/nuts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/bench/nuts/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/nuts/__pycache__/config.cpython-310.pyc,,
falcon/bench/nuts/__pycache__/setup.cpython-310.pyc,,
falcon/bench/nuts/config.py,sha256=4DaxpHr2QKR7iLRJGZCdgSVe_k7WH_Mky5Musfhvc-A,1084
falcon/bench/nuts/nuts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/bench/nuts/nuts/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/nuts/nuts/__pycache__/app.cpython-310.pyc,,
falcon/bench/nuts/nuts/app.py,sha256=CWV1BWHKcWl_Bx5PLrxfjbcOzcW638kEqy3viYD7mC4,512
falcon/bench/nuts/nuts/controllers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/bench/nuts/nuts/controllers/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/nuts/nuts/controllers/__pycache__/root.cpython-310.pyc,,
falcon/bench/nuts/nuts/controllers/root.py,sha256=3pW_2nPdEQtASiCpVL7TGw4KPyTMmIk0qXa-9i91vkY,823
falcon/bench/nuts/nuts/model/__init__.py,sha256=F06sMn7gD0_IgRvoUpJbpQXWFSvQxjJTkImezlwt_aU,469
falcon/bench/nuts/nuts/model/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/nuts/nuts/tests/__init__.py,sha256=HP2LeL1tfjqbWtMoBfJfZCGsMhl5JTUuMOT-gz6DySw,504
falcon/bench/nuts/nuts/tests/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/nuts/nuts/tests/__pycache__/config.cpython-310.pyc,,
falcon/bench/nuts/nuts/tests/__pycache__/test_functional.cpython-310.pyc,,
falcon/bench/nuts/nuts/tests/__pycache__/test_units.cpython-310.pyc,,
falcon/bench/nuts/nuts/tests/config.py,sha256=FHEge4Ephjusuk34Us_je2GTLj19y6Ai6r7m0WAVdAI,537
falcon/bench/nuts/nuts/tests/test_functional.py,sha256=JfKlq35wy8cPOzz7F7UH_tKyBXX9XqvcJREiBzn_VQw,689
falcon/bench/nuts/nuts/tests/test_units.py,sha256=4T6HIcUEYiDh8cyerb57CKiC1DTcgkxXklIwBBheN7s,118
falcon/bench/nuts/setup.py,sha256=4h6cKotv10k70NANJG6x0yzDU1nblHeLSE0-id_ZTqA,461
falcon/bench/queues/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/bench/queues/__pycache__/__init__.cpython-310.pyc,,
falcon/bench/queues/__pycache__/api.cpython-310.pyc,,
falcon/bench/queues/__pycache__/claims.cpython-310.pyc,,
falcon/bench/queues/__pycache__/messages.cpython-310.pyc,,
falcon/bench/queues/__pycache__/queues.cpython-310.pyc,,
falcon/bench/queues/__pycache__/stats.cpython-310.pyc,,
falcon/bench/queues/api.py,sha256=jCozc5rEb7d4amF7_obiXoPHGxhXaRCr-uNvuPOXcTg,2673
falcon/bench/queues/claims.py,sha256=nTBDQD33mcenfqAP8r-Q27eljqOKFrPjMU1asskE1rU,978
falcon/bench/queues/messages.py,sha256=hY3D0ZoP_NI7tLJKopn8Hnu6-D1x6cUFKfwirxdPVws,970
falcon/bench/queues/queues.py,sha256=-ij4GyWerZNX5jNduk7gPz34PGXizS4cnwCQOi47SwY,933
falcon/bench/queues/stats.py,sha256=MVc5YcQeaAfucx3mfpwSjlahqBNi3c_p3pV4SJPTx7Y,690
falcon/cmd/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/cmd/__pycache__/__init__.cpython-310.pyc,,
falcon/cmd/__pycache__/bench.cpython-310.pyc,,
falcon/cmd/__pycache__/inspect_app.cpython-310.pyc,,
falcon/cmd/bench.py,sha256=_8-BTIVWLMaNW6NHE1hmnNoHl1w31EHQv_aTlGyFr3k,975
falcon/cmd/inspect_app.py,sha256=3199gMvg_vVFC_e3xITQVJ0h3V7SGdL351qLknMs-xY,3371
falcon/constants.cp310-win_amd64.pyd,sha256=eaK0YmOKXuU5hwJOlfhjXTzg6w6-thjIZ5JYR8w5ZJE,68096
falcon/constants.py,sha256=4OMLDZBSf0DFdg1vJrArUV8icQZp8FlSqJCZo3VZSsI,5542
falcon/cyutil/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/cyutil/__pycache__/__init__.cpython-310.pyc,,
falcon/cyutil/misc.cp310-win_amd64.pyd,sha256=aHLMyBIosROGTSKOrrygYKewwGmXxcgFJ-Nrvax-sKw,34304
falcon/cyutil/misc.pyx,sha256=9VegGMHYoeNz17pDdnMcVEEzx3Rd0XIexU5EXO97e1M,858
falcon/cyutil/reader.cp310-win_amd64.pyd,sha256=FywfPymkMizYZALa3xY3CCIPiz66bybtGVuiPofxsW8,86528
falcon/cyutil/reader.pyx,sha256=_5oEMAqyj1kbzGdA0tCOSBWe8M7-P3GXE8v0AWU0ra4,15667
falcon/cyutil/uri.cp310-win_amd64.pyd,sha256=PN2xLANsWkEzvoPmvQe_bbyU3Uz4iPX-t8JCVOn_URU,53760
falcon/cyutil/uri.pyx,sha256=otb9caZ9ZIER4jdWws-d4_Gk3Qj6b5hxpqGc-oCEUhY,9599
falcon/errors.cp310-win_amd64.pyd,sha256=5Dd6QnGWbb5aPJH1k7ULOM2iJnjFwQ0KAY2V8ytXApE,307712
falcon/errors.py,sha256=0b_hiASl504ERiPEfAZYIDSNYVWqIEhDIfsnscQVcJ8,110847
falcon/forwarded.cp310-win_amd64.pyd,sha256=D7ITL4STikJTysmBWyuE-A6DBhLUXPoSel698NFMWaI,76800
falcon/forwarded.py,sha256=J4Q-ZNHzBs9DyZvRJSCA97rOwsys3cfLKNkuWjxKrG0,6783
falcon/hooks.py,sha256=tZYysGULsAAceS9YB_BVar93FYVIs0HSjiuYLEMrZmo,12401
falcon/http_error.cp310-win_amd64.pyd,sha256=KFRq67Rq53Fn1AiZ8i-Vo3Gc7rot-ZbVFB8mo-nYQDk,77312
falcon/http_error.py,sha256=4GIZGhUdUPn7ttBhCFk4GnE5JbSL-27iP8tMS9C0JAM,9475
falcon/http_status.cp310-win_amd64.pyd,sha256=jCuB4sXTBPUDJnmMTeN0sEMyQdR34YL5Z-eUCmZNND8,44544
falcon/http_status.py,sha256=f2HSDTmZOV8UGaJWA74fq7pz0ADCvN4LQNufaE4yXlQ,2457
falcon/inspect.py,sha256=N2BaD0ja5TgFHLhuiW2PtSUGmKYKag4EGIqIWm0ejm8,29138
falcon/media/__init__.py,sha256=MqTF_ATASoW97OoqijnkIbO-RZFNbcqNw2n1ElVEW-o,726
falcon/media/__pycache__/__init__.cpython-310.pyc,,
falcon/media/__pycache__/base.cpython-310.pyc,,
falcon/media/__pycache__/handlers.cpython-310.pyc,,
falcon/media/__pycache__/json.cpython-310.pyc,,
falcon/media/__pycache__/msgpack.cpython-310.pyc,,
falcon/media/__pycache__/multipart.cpython-310.pyc,,
falcon/media/__pycache__/urlencoded.cpython-310.pyc,,
falcon/media/base.cp310-win_amd64.pyd,sha256=_qdT1jzmIbwe-XQ_GH9J8IISuPgAcag5QjrQd29vc1Q,86016
falcon/media/base.py,sha256=EZXj6GdBx9UId2lUgnRlTOlopzhTpqRt7JclLcvlsOg,9567
falcon/media/handlers.cp310-win_amd64.pyd,sha256=3EdvE3hjDUgi47KgOR4oUaJnA2D7Dr6b1HmYX2wIByc,97280
falcon/media/handlers.py,sha256=QP_05UsUgumFzsPjZdSG9xfi3cIRkxD-thWDT5TJ_BY,7886
falcon/media/json.cp310-win_amd64.pyd,sha256=_oVZfdes17JEo1zXnLNhwnPolsXXFFVjEOACnxHVUEU,106496
falcon/media/json.py,sha256=peGT7SBJ2r8phl9BJMO1egYLiZVO8r23msqL5ON7Dgk,11178
falcon/media/msgpack.cp310-win_amd64.pyd,sha256=jmoDggFv35UYr6c_DItpFbXnR-qcjaBJZo1aedLfKNY,90624
falcon/media/msgpack.py,sha256=WhwbnpOCOZRl264dX8q3BscfpMVH3-M4QkvtB5R-RVM,4101
falcon/media/multipart.cp310-win_amd64.pyd,sha256=2WXfNdGL4Frp_dqWTbvTbiPaOm7VgT6f42YT4njrXes,164864
falcon/media/multipart.py,sha256=RPdZSXFThl-O--Bh-Y88Y6yj5-Ak2NQaPDAnieyRy70,23836
falcon/media/urlencoded.cp310-win_amd64.pyd,sha256=v_NGKBEOy0vJ6e4r3elJaDzEPVREASACq4jtqayvvqo,76800
falcon/media/urlencoded.py,sha256=t8agPzTThB-MwTPt5Fne8V-bmzZ57gxssMuEC4NmpoI,3203
falcon/media/validators/__init__.py,sha256=UPSdQrB0C4_rOBIACGygcXVHZYraIV6FrOLuiXyRFbA,26
falcon/media/validators/__pycache__/__init__.cpython-310.pyc,,
falcon/media/validators/__pycache__/jsonschema.cpython-310.pyc,,
falcon/media/validators/jsonschema.py,sha256=ToJHP2FL4f0X5alvbAGdNU2VBATlLeJckRiHfx7xQU0,5756
falcon/middleware.cp310-win_amd64.pyd,sha256=eJ7S8hBY-OL9A8oUgptuD9LLQJAeWaaVLKIm1ZJrSBY,76800
falcon/middleware.py,sha256=pkt7iN1LzLdeRZqkwzqyVokAJ2NHv7xtJIBTPf1tmdk,6254
falcon/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
falcon/redirects.cp310-win_amd64.pyd,sha256=LWQFZpNN92CFaxYfaevx8-6jKudohpDLrk_UdhbfuWY,62976
falcon/redirects.py,sha256=5bzOOeAuP8h1evjVy6YubqoJQoz93hGIwQLpLN0b6cA,5871
falcon/request.cp310-win_amd64.pyd,sha256=ZVB0umIDPB7H52ZFgIwCXy2kxR1SS4lxDY6_zW23cLg,402432
falcon/request.py,sha256=vR-0GFuENqm7eRfDCBiEp635mHCbb5V8To82FulSoY0,95305
falcon/request_helpers.cp310-win_amd64.pyd,sha256=i3hSA9muAQGj1sxMUHqq5f7h_tLymK0HkGZP8EeeR4I,62976
falcon/request_helpers.py,sha256=gRcg0MqyRZfs_iRjrLhAtZ6wMrscrxakUtjPxkg8MRQ,6469
falcon/responders.py,sha256=TBqRWm_H8TYRJRqngnYawsNq37W66TGfnJtfsywquds,3938
falcon/response.cp310-win_amd64.pyd,sha256=4oMzYQzeNz3Aunc0AEZS220To_GdKPmncZ8YSyKpTgo,198656
falcon/response.py,sha256=AmTDD2SZ2OLa1oxgbLZpippmbiO0-LPGhf12wvlkFp8,57448
falcon/response_helpers.cp310-win_amd64.pyd,sha256=1DTKdOFRsJZrxqUZMyyxclxs-UXQXe3-RSLbz0iN3mE,69120
falcon/response_helpers.py,sha256=9qRU6HuhMx0hvHtI7jHwXSVa_9bW2foqWtEU5KxjYGk,4889
falcon/routing/__init__.py,sha256=OlNGvJLst2XaAZT8am9LyUvIYPcNzEzg4ZpaopxEgCE,1482
falcon/routing/__pycache__/__init__.cpython-310.pyc,,
falcon/routing/__pycache__/compiled.cpython-310.pyc,,
falcon/routing/__pycache__/converters.cpython-310.pyc,,
falcon/routing/__pycache__/static.cpython-310.pyc,,
falcon/routing/__pycache__/util.cpython-310.pyc,,
falcon/routing/compiled.cp310-win_amd64.pyd,sha256=A6dn6So6pZzo-X3TOpp1SURTCspktsX173UVnyqLXcg,310272
falcon/routing/compiled.py,sha256=VMbuWMczm2R4y92CDNqwmpazbWacHpbeRqp85xipUG4,46700
falcon/routing/converters.cp310-win_amd64.pyd,sha256=D5usQYlSFoZvHKAcLoo3c9DEdIJ1DbAylancIrCsDQE,88576
falcon/routing/converters.py,sha256=KUmM6XnNlniZrAb2YH2XKkKmHES5aeuFd9FvGygzR-M,8117
falcon/routing/static.cp310-win_amd64.pyd,sha256=OsVxRoxAN4cZbmWDc0QYR-ApA-1wbSqtKX0Ti1oYkY4,134144
falcon/routing/static.py,sha256=-Tnu4U8PPEy33s3FMgej6thPudx69DJDoK-DPOoyjdc,10760
falcon/routing/util.cp310-win_amd64.pyd,sha256=Eguy0Vff7D8hgQ7L7ua9rjRircB1N-2oAOBR9gxdW-Y,55296
falcon/routing/util.py,sha256=AayTzuzFF8oT1TXhYujf4t2d0PD6IFD1l5jHytVw2Go,3868
falcon/status_codes.cp310-win_amd64.pyd,sha256=POOBii19IkyvFzcW83XiaQw0Xvyft48Zn1YaGeBjNJA,72704
falcon/status_codes.py,sha256=2FA59CAjmtB0SY_ZZEezTJkjBTd4ntLyyRYMUFoIwBU,13011
falcon/stream.cp310-win_amd64.pyd,sha256=1j_6DMXPHWvmRTahNb8NdmShDhWqvy2_-nRzwc7gJIY,72704
falcon/stream.py,sha256=3bYVKAMjCGAiCWI1P0aIL6Lr30UE5p-WWiqto3NYb0Y,5912
falcon/testing/__init__.py,sha256=sMUX6tXW1TK9RkB49eIFVPqD0-GGqGYJIcZeRrcLskc,5728
falcon/testing/__pycache__/__init__.cpython-310.pyc,,
falcon/testing/__pycache__/client.cpython-310.pyc,,
falcon/testing/__pycache__/helpers.cpython-310.pyc,,
falcon/testing/__pycache__/resource.cpython-310.pyc,,
falcon/testing/__pycache__/srmock.cpython-310.pyc,,
falcon/testing/__pycache__/test_case.cpython-310.pyc,,
falcon/testing/client.py,sha256=C0R7GpekTU5wXoBhcdbmGskgDgW8ennxq4dGejfCtg0,100557
falcon/testing/helpers.py,sha256=EnNwi8P06P-C8zSBsWkKzuF5ZXHnUrxuNxXDIH6gjpc,58605
falcon/testing/resource.py,sha256=lMuTy-9XRutc-9kpUrHCsFscK1qCQ2K0AE4RMtuV4go,10167
falcon/testing/srmock.py,sha256=Sate_yHMQLmwoC0swKVqQCgeIqI9FGAhi5boxb4K6Q8,2354
falcon/testing/test_case.py,sha256=e8Dqsi07vprhM242lzoV_QZaC62QFlkhgD_38DdO3oo,3035
falcon/typing.cp310-win_amd64.pyd,sha256=kYQOL28r2pBWv5kXcoAG_MENqA5zIsXiulerJ24thLM,64512
falcon/typing.py,sha256=Sl130F8xxlIf-TWHM_R0HLRpbVFAzVYIR5yZU6u2cxE,1612
falcon/uri.cp310-win_amd64.pyd,sha256=CW--ltt5_OMlNV6BhWT_GXW8lPfL00EsNFH_3DTVqjw,23040
falcon/uri.py,sha256=tlwmoRzAhmUcXRqVno0eh50MgWQ_z5SAxl5_ib7FifI,847
falcon/util/__init__.py,sha256=i7ztYa-FDb4yDwWeTnn5zcZAYLPrQ_xHgyRE0VUvpUQ,3070
falcon/util/__pycache__/__init__.cpython-310.pyc,,
falcon/util/__pycache__/deprecation.cpython-310.pyc,,
falcon/util/__pycache__/mediatypes.cpython-310.pyc,,
falcon/util/__pycache__/misc.cpython-310.pyc,,
falcon/util/__pycache__/reader.cpython-310.pyc,,
falcon/util/__pycache__/structures.cpython-310.pyc,,
falcon/util/__pycache__/sync.cpython-310.pyc,,
falcon/util/__pycache__/time.cpython-310.pyc,,
falcon/util/__pycache__/uri.cpython-310.pyc,,
falcon/util/deprecation.cp310-win_amd64.pyd,sha256=px8aNe3ZzI6bNi69iwhvr8elNz0FuW-HfqiUg3aX4cQ,65536
falcon/util/deprecation.py,sha256=q7L4swsQ-_Qh7TTFuQ_XbxKH1_18gpUHgcEaF5IEmlw,4235
falcon/util/mediatypes.cp310-win_amd64.pyd,sha256=T1lTAI9pRG-zc5bM5NIHs6L65BNJe_PkjuuB4HztDbI,133120
falcon/util/mediatypes.py,sha256=763gtBxU5qRn8eh5khTStBWVDWKJbbBIU2y-luYucbU,12124
falcon/util/misc.cp310-win_amd64.pyd,sha256=wrjR5My1BTgdjm-UeFZJwGvRbX_2JNkqUTNnshx16MI,110080
falcon/util/misc.py,sha256=nhjovMBiKQpgMe87w61tHQ8mADPcPMDopZxwxSfGm3A,16582
falcon/util/reader.py,sha256=NSpvn8LGHsDBPaZv-icfWHnLiRsjrPZTsPAKL8nob2A,15102
falcon/util/structures.cp310-win_amd64.pyd,sha256=usPsuyP8ke76P7Dx29q_TOMXzsaDgV2XjK3SN84RUnQ,142848
falcon/util/structures.py,sha256=ED4AqkIJi2eYA1vB2j0mBZpvakhxFCDgyrMAtOvj5Oc,11466
falcon/util/sync.py,sha256=rtRn7vnDp4SdJdFku8zfz1G23Ane3LWPFZVvHIHTR8Y,10579
falcon/util/time.cp310-win_amd64.pyd,sha256=vQs8qZbL1teOO1YOV9XlQJMka9EMqP2vUuSysvJRUII,48128
falcon/util/time.py,sha256=61ju-0TMcppIJOiwWVtB1t_D9NQw9hG_wPEvsDF5Okk,1908
falcon/util/uri.cp310-win_amd64.pyd,sha256=dQQ4_UtTWBI9R08ooWBIqm0Cygc2ehSww0tqaVnOVuI,118272
falcon/util/uri.py,sha256=o8BiR9QpkPDCaU30_BaCEbsRn74K19MksEfafarTnFg,21410
falcon/version.cp310-win_amd64.pyd,sha256=yyFFasAKOUbmgoYx7dHfvhGCzxyIwHSezwv4258A0ao,19456
falcon/version.py,sha256=HZlbUd3hy0gZv7tQRwS_VCk8GrdWkrtIz2RgAw7l_7c,686
