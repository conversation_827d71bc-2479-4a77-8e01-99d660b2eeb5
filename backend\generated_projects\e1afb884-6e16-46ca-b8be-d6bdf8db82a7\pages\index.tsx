import type { AppProps } from 'next/app'
import type { Element } from 'react'
import type { AppContext, Props } from 'next/app'
import type { StripeProvider, Elements } from 'react-stripe-elements'
import type { Stripe } from '@stripe/stripe-js'
import MyApp from 'components/MyApp'
import 'styles/globals.css'
const stripePromise = loadStripe(process.env.STRIPE_PUBLISHABLE_KEY!)
function MyApp({ Component, pageProps }: AppProps) {
return (
<StripeProvider stripe={stripePromise}>
<Elements stripe={stripePromise}>
<Component {...pageProps} />
</Elements>
</StripeProvider>
)
}
MyApp.getInitialProps = async ({}: AppContext) => {
return {}
}
export default MyApp