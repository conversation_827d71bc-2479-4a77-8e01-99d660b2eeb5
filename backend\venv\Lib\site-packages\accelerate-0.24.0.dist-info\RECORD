../../Scripts/accelerate-config.exe,sha256=a9i2ZieLvud_GwR1sAaonP--iitN3QnOYSajvw4t8jQ,107923
../../Scripts/accelerate-estimate-memory.exe,sha256=2cTFHB_h1p2bsIav2kl6X9-85LeD8YMffsfbqUUpbIk,107925
../../Scripts/accelerate-launch.exe,sha256=GIqKKP1VukjCQjhbcS6VZBHEiCCsMaAmwj7pO5EXMx8,107923
../../Scripts/accelerate.exe,sha256=mXf2o3mikNVAOFVUZOqVbR-YC6x3NdYnYcYDgEjfo6Q,107931
accelerate-0.24.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
accelerate-0.24.0.dist-info/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
accelerate-0.24.0.dist-info/METADATA,sha256=F6qRQ7PD69IsvL07MMXCmMvUdpeyo6buCmk5WJXsazs,18080
accelerate-0.24.0.dist-info/RECORD,,
accelerate-0.24.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate-0.24.0.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
accelerate-0.24.0.dist-info/entry_points.txt,sha256=Z_KV59tIt4oZtUDEQ0w8JThJ6_1dd8vR8heH24DeAXI,238
accelerate-0.24.0.dist-info/top_level.txt,sha256=esVfdxTidsjQ90zsN_rPpjLFJ4ijRlx4mnLrG09hlt4,11
accelerate/__init__.py,sha256=U7C6NnYZxCPm5CY8ce4k1JAz99vpnK6HTYzFsQn9bas,784
accelerate/__pycache__/__init__.cpython-310.pyc,,
accelerate/__pycache__/accelerator.cpython-310.pyc,,
accelerate/__pycache__/big_modeling.cpython-310.pyc,,
accelerate/__pycache__/checkpointing.cpython-310.pyc,,
accelerate/__pycache__/data_loader.cpython-310.pyc,,
accelerate/__pycache__/hooks.cpython-310.pyc,,
accelerate/__pycache__/launchers.cpython-310.pyc,,
accelerate/__pycache__/local_sgd.cpython-310.pyc,,
accelerate/__pycache__/logging.cpython-310.pyc,,
accelerate/__pycache__/memory_utils.cpython-310.pyc,,
accelerate/__pycache__/optimizer.cpython-310.pyc,,
accelerate/__pycache__/scheduler.cpython-310.pyc,,
accelerate/__pycache__/state.cpython-310.pyc,,
accelerate/__pycache__/tracking.cpython-310.pyc,,
accelerate/accelerator.py,sha256=Y-1bP4dpYRkfY2pIKat1rfDTpPiSXAM_QXBde51lSLw,142059
accelerate/big_modeling.py,sha256=R-PEOG72s-ZssOoOSVUhm0FrPGTTi5f6NhikWN5gtu0,25373
accelerate/checkpointing.py,sha256=3438BC0G8my8ESFJB-twiUm4_ytaOe-HmMt69f4xSG0,10436
accelerate/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate/commands/__pycache__/__init__.cpython-310.pyc,,
accelerate/commands/__pycache__/accelerate_cli.cpython-310.pyc,,
accelerate/commands/__pycache__/env.cpython-310.pyc,,
accelerate/commands/__pycache__/estimate.cpython-310.pyc,,
accelerate/commands/__pycache__/launch.cpython-310.pyc,,
accelerate/commands/__pycache__/test.cpython-310.pyc,,
accelerate/commands/__pycache__/tpu.cpython-310.pyc,,
accelerate/commands/accelerate_cli.py,sha256=HAf0-_GVXAlQtTXyIJ3maN8BKOUKdKVL6W7TFPFojr0,1721
accelerate/commands/config/__init__.py,sha256=iJK8dgj3pc5Vdr1E7UuGoFu-BlybyXLxYDoTg9gXngE,1645
accelerate/commands/config/__pycache__/__init__.cpython-310.pyc,,
accelerate/commands/config/__pycache__/cluster.cpython-310.pyc,,
accelerate/commands/config/__pycache__/config.cpython-310.pyc,,
accelerate/commands/config/__pycache__/config_args.cpython-310.pyc,,
accelerate/commands/config/__pycache__/config_utils.cpython-310.pyc,,
accelerate/commands/config/__pycache__/default.cpython-310.pyc,,
accelerate/commands/config/__pycache__/sagemaker.cpython-310.pyc,,
accelerate/commands/config/__pycache__/update.cpython-310.pyc,,
accelerate/commands/config/cluster.py,sha256=tCt5CBkGT4b96DUf7hqAimFMcefVL8hcDxtDu6d2qGo,27995
accelerate/commands/config/config.py,sha256=FuRlQvOjgATEtyqOSsGD-KEtOCvACOHjs2C-krrtldk,3035
accelerate/commands/config/config_args.py,sha256=neMxebxNBe2K3cO9pkvVE2Z7PK7Nx1x8mVOfo-RAVTc,9633
accelerate/commands/config/config_utils.py,sha256=Wpsl7zFDuSq4IGm-72DdJvdkwMe2cy-PxoJruxB7IpA,2913
accelerate/commands/config/default.py,sha256=LnqA8EnI7kRpjqYlTPM2ZAscxsB2XeIQZgGOaTTy2N4,5044
accelerate/commands/config/sagemaker.py,sha256=GjHE2-h4tRr1P_PFtMF3miiAtJlzkbHbMb6kFXqn8eo,10341
accelerate/commands/config/update.py,sha256=NXW1J7GkUHpg71QlIXsmMB_0z8S8IZo2FWax5POwrhc,2395
accelerate/commands/env.py,sha256=7guiNUOE0SFe2CRexn2FNmI-4yuwGOXSJLd3QG8tVpA,3056
accelerate/commands/estimate.py,sha256=xVSJ_4v471PNA8an6MMfykwWF4V3NdltGCpES0eVSiw,10429
accelerate/commands/launch.py,sha256=ygZfvgclOqeuXCsnzTPYUtxIt6xaD9k7cDSXPBq9KwE,38678
accelerate/commands/menu/__init__.py,sha256=5EhDZN5_e1TAuh9_KqJ4Ghs61offoeGZy1pktSBDpa0,39
accelerate/commands/menu/__pycache__/__init__.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/cursor.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/helpers.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/input.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/keymap.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/selection_menu.cpython-310.pyc,,
accelerate/commands/menu/cursor.py,sha256=-lmpJVAzvNc0c3EOtSuLoKB59zqylVCbYyWLPnrOmvQ,2028
accelerate/commands/menu/helpers.py,sha256=KrSB5fJjH4MUEUAQJ6bYaN16AYcnl9UalDrPD3DYeeg,1483
accelerate/commands/menu/input.py,sha256=uW2ywuqWPOKjkS7XBjqNpuVWLTgVKici2_xLyltEbMs,2581
accelerate/commands/menu/keymap.py,sha256=c9YEMMmNlBGtMiWFk2rdhtTSCZ9w_uJ77cNCwAKguHk,4087
accelerate/commands/menu/selection_menu.py,sha256=UZKwSIZKKG60y2fuWbSoCx0RbrPS4MbY2DwvxWRBIBQ,4920
accelerate/commands/test.py,sha256=whf_g7X263A5OErEHRzKu_L5x6HWbIIVNS8N5ERtGao,2179
accelerate/commands/tpu.py,sha256=OnFQNu9zhlK5D7xXouZZXJevN5623Jgy_HsHTuy4HAE,5553
accelerate/data_loader.py,sha256=hQaTBUZpV5589G4U0QTthYbfCbqXLPzSqFXnXHBs4BE,45653
accelerate/hooks.py,sha256=ONXOaJq9hyy1Q_acpUrEz5ASXSw-hwO0cOTPEfikZKE,24826
accelerate/launchers.py,sha256=DCfML0oLdrpTxW9AJZzt9o0cdt0ErsXKz3-8S90e-iE,10357
accelerate/local_sgd.py,sha256=znJcwwpRb0imRslW5_uQ4OYJmM8zxekMv4XTnbzXlZk,3924
accelerate/logging.py,sha256=tVWmQcCDK5SvPDOUv0ytAaIseV_0k_rhz_h7YywyxVc,4282
accelerate/memory_utils.py,sha256=3R5LoeHl6GgTZ-IMPrDZMdaEehWarGdPqODushb-6pg,862
accelerate/optimizer.py,sha256=A48WIWqZgaq_MpWTfoK4QwOHorjk4EUK4fUeWBPr0jg,7205
accelerate/scheduler.py,sha256=des_4M_Tt1W8gCYZZbLla0GHBEgJY3Wx2EGBQPTzeiY,4238
accelerate/state.py,sha256=vjse7lb_au0poyH33uq1KvN_QiQhTDFGOydVvzhYRp0,44317
accelerate/test_utils/__init__.py,sha256=Tp-oHHpeCkzi810EsvKSTk0GQeMQ1ybM6U96b2BJcwQ,556
accelerate/test_utils/__pycache__/__init__.cpython-310.pyc,,
accelerate/test_utils/__pycache__/examples.cpython-310.pyc,,
accelerate/test_utils/__pycache__/testing.cpython-310.pyc,,
accelerate/test_utils/__pycache__/training.cpython-310.pyc,,
accelerate/test_utils/examples.py,sha256=PJAAy5MjIeyH5Sgj9sFqh0VGebfI7Tg4i_3OBABVVYg,7301
accelerate/test_utils/scripts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate/test_utils/scripts/__pycache__/__init__.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_cli.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_distributed_data_loop.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_notebook.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_ops.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_script.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_sync.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate/test_utils/scripts/external_deps/__pycache__/__init__.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_checkpointing.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_metrics.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_peak_memory_usage.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_performance.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/test_checkpointing.py,sha256=eJ8dpY6Bi9De7Vb9oDw435NELTjWegjWD7wuckvkaoQ,10686
accelerate/test_utils/scripts/external_deps/test_metrics.py,sha256=ahmtl--BAkXKF53SUGi0oZdhWzB-2Tt3GMjLa3RfVJc,10973
accelerate/test_utils/scripts/external_deps/test_peak_memory_usage.py,sha256=xC5WLrPk87fSdf8Pr_JvM4BhjT4ngnHX831UK3BnZEw,10725
accelerate/test_utils/scripts/external_deps/test_performance.py,sha256=VqYjGaIK509389-iukIBb1397dFLbrWyHwjAKF3Fcvw,9093
accelerate/test_utils/scripts/test_cli.py,sha256=EJClouXlerf7cpgqY1P1VY2ohUcRXk56GoVkM6-jmrU,227
accelerate/test_utils/scripts/test_distributed_data_loop.py,sha256=TEqnW4WIlSDsMB9D2bcvAC_AB9hHtJRiERt3ZGKzK80,8236
accelerate/test_utils/scripts/test_notebook.py,sha256=oUtN-NTgRXEc4Xm4xsq5gaCnVv_nH-czQ4h5haF2kI0,445
accelerate/test_utils/scripts/test_ops.py,sha256=998SE9udf66cyD0JLjEWZuKvQVZ13rSk_4KdnJ0qliY,5263
accelerate/test_utils/scripts/test_script.py,sha256=1owJXDVRV50hdZgsd1aPHbcuTkkFyTVxRJaZwcV-f0E,24669
accelerate/test_utils/scripts/test_sync.py,sha256=PHtgg7jZpkeE2hMjLRV7YGLRdNiatbR_G76tVVZKimY,17147
accelerate/test_utils/testing.py,sha256=wIJm--2P3FXbGDWyRLDwWM0_BC6vYFl7KZU2KsZ-ISM,15500
accelerate/test_utils/training.py,sha256=7RNVMmRb6WFCvGzyR2tWTaPL5tKO4YGzjXN0GFWvI8U,4019
accelerate/tracking.py,sha256=feW96z-oQab05q_P3cv1bemraNszrNR9KF6DkHQOFPI,28517
accelerate/utils/__init__.py,sha256=sPA8HbK7DvQBKrYFGLFCEHMYte6sytuMU7n0hYRSzJE,4784
accelerate/utils/__pycache__/__init__.cpython-310.pyc,,
accelerate/utils/__pycache__/bnb.cpython-310.pyc,,
accelerate/utils/__pycache__/constants.cpython-310.pyc,,
accelerate/utils/__pycache__/dataclasses.cpython-310.pyc,,
accelerate/utils/__pycache__/deepspeed.cpython-310.pyc,,
accelerate/utils/__pycache__/environment.cpython-310.pyc,,
accelerate/utils/__pycache__/fsdp_utils.cpython-310.pyc,,
accelerate/utils/__pycache__/imports.cpython-310.pyc,,
accelerate/utils/__pycache__/launch.cpython-310.pyc,,
accelerate/utils/__pycache__/megatron_lm.cpython-310.pyc,,
accelerate/utils/__pycache__/memory.cpython-310.pyc,,
accelerate/utils/__pycache__/modeling.cpython-310.pyc,,
accelerate/utils/__pycache__/offload.cpython-310.pyc,,
accelerate/utils/__pycache__/operations.cpython-310.pyc,,
accelerate/utils/__pycache__/other.cpython-310.pyc,,
accelerate/utils/__pycache__/random.cpython-310.pyc,,
accelerate/utils/__pycache__/rich.cpython-310.pyc,,
accelerate/utils/__pycache__/torch_xla.cpython-310.pyc,,
accelerate/utils/__pycache__/tqdm.cpython-310.pyc,,
accelerate/utils/__pycache__/transformer_engine.cpython-310.pyc,,
accelerate/utils/__pycache__/versions.cpython-310.pyc,,
accelerate/utils/bnb.py,sha256=3i59dy8EcBYJEnT2alJ5_M-zeIpFsrceQ4bImiJJKOk,20570
accelerate/utils/constants.py,sha256=cqYWtpg9YSuBZwsXmne-GciNVostmglspjAMyqvJcFo,2474
accelerate/utils/dataclasses.py,sha256=TdMDiwREZjjBfIjx5F466EGwSW4AY1mAqPP9QfJmWd0,64976
accelerate/utils/deepspeed.py,sha256=X_JG9XW-ZGVmXGED84eC2akEkUt-YsNiewCSeW7DYLk,10208
accelerate/utils/environment.py,sha256=K0xxdlZVVaSHVxDmDm77R7VSQcnj4DosHgLTV3d7BWs,1750
accelerate/utils/fsdp_utils.py,sha256=pKhY3BSI10MrqLtrnvF_7vBFVb9L_nYJSd2xTDbiDFs,9911
accelerate/utils/imports.py,sha256=eE9FB95A4-g6XDXK-v7WQnMbCTIL3qYtLWyq8rgZw24,9241
accelerate/utils/launch.py,sha256=LuA0aSzosAFy7rHawQ6YIWNkpiBN5hzzqBPS7rOuOJc,24412
accelerate/utils/megatron_lm.py,sha256=yOrhJ2u9NKBO3LR_FWlIxh44PWUx_cgdcCVDAjrBiE8,57263
accelerate/utils/memory.py,sha256=d2DBzqkcoYAPlpK0aMQ5f5c-R-M6Wx9KBx_2UM6qhNw,4880
accelerate/utils/modeling.py,sha256=gHibZ4581i0ErrwTZcjcUJxbHJPpyXGWcTintjGz0AU,65621
accelerate/utils/offload.py,sha256=EBFWsgJ-9h_a029VRSOED971TqPqyCsvcdKFX2H-Z_g,7626
accelerate/utils/operations.py,sha256=glbbTueFksp4vJCjJ3boaxnvyQbbhkt7tLXaByou7Jo,24161
accelerate/utils/other.py,sha256=33aioqqqUiKC4F4WSue41dVzygD-magCab60HCi3hdU,8415
accelerate/utils/random.py,sha256=IWVnFFjRuZZOO8HI9L7suHRSM33Pk2NXYywOpU0BKIg,4292
accelerate/utils/rich.py,sha256=8JZX_uGMQX-BufdXxJpdne7BWd1KyLHSgbiGxrDMYr8,847
accelerate/utils/torch_xla.py,sha256=Pq1tuqN0X_pWDVza6YgjfO45uoJdoRVRForLeLQzFus,1908
accelerate/utils/tqdm.py,sha256=0cegNnuA93tKT3o6HDip90rPl8BODLFLu4jP1E3aJ08,1344
accelerate/utils/transformer_engine.py,sha256=TlbaYL85ppjFD3DUgkUopTJkVIWxQOk476EpGb2LJ58,3665
accelerate/utils/versions.py,sha256=UgmcbjBm--6CIx1ZamSAMjAK_B_2l48LbeaNygqej8M,2149
