#!/usr/bin/env python3
"""
Test script to check authentication and token endpoints
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_register_and_login():
    """Test user registration and login"""
    print("=== Testing Authentication ===")
    
    # Test registration
    register_data = {
        "username": "testuser",
        "email": "<EMAIL>", 
        "password": "testpass123",
        "full_name": "Test User"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/register", json=register_data)
        if response.status_code == 200:
            print("✓ Registration successful")
        elif response.status_code == 400 and "already registered" in response.text:
            print("✓ User already exists (expected)")
        else:
            print(f"✗ Registration failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"✗ Registration request failed: {e}")
        return None
    
    # Test login
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/token", 
            data=login_data,  # OAuth2PasswordRequestForm expects form data
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        if response.status_code == 200:
            token_data = response.json()
            print("✓ Login successful")
            return token_data["access_token"]
        else:
            print(f"✗ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"✗ Login request failed: {e}")
        return None

def test_token_endpoints(token):
    """Test token-related endpoints"""
    if not token:
        print("✗ No token available for testing")
        return
        
    print("\n=== Testing Token Endpoints ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test /api/users/me
    try:
        response = requests.get(f"{BASE_URL}/api/users/me", headers=headers)
        if response.status_code == 200:
            user_data = response.json()
            print(f"✓ /api/users/me successful: {user_data['username']}")
        else:
            print(f"✗ /api/users/me failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ /api/users/me request failed: {e}")
    
    # Test /api/tokens/info
    try:
        response = requests.get(f"{BASE_URL}/api/tokens/info", headers=headers)
        if response.status_code == 200:
            token_data = response.json()
            print(f"✓ /api/tokens/info successful: {token_data['remaining_tokens']} tokens remaining")
        else:
            print(f"✗ /api/tokens/info failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ /api/tokens/info request failed: {e}")
    
    # Test /users/projects
    try:
        response = requests.get(f"{BASE_URL}/users/projects", headers=headers)
        if response.status_code == 200:
            projects = response.json()
            print(f"✓ /users/projects successful: {len(projects)} projects found")
        else:
            print(f"✗ /users/projects failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ /users/projects request failed: {e}")

def test_chat_endpoint(token):
    """Test chat endpoint"""
    if not token:
        print("✗ No token available for testing")
        return
        
    print("\n=== Testing Chat Endpoint ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test basic chat
    chat_data = {
        "message": "Hello, how are you?",
        "model": "basic"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/chat", json=chat_data, headers=headers)
        if response.status_code == 200:
            chat_response = response.json()
            print(f"✓ Basic chat successful: {chat_response['response'][:50]}...")
        else:
            print(f"✗ Basic chat failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ Basic chat request failed: {e}")
    
    # Test project-context chat
    project_chat_data = {
        "message": "Add a new component to this project",
        "model": "basic",
        "project_id": "test-project",
        "project_path": "/path/to/project"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/chat", json=project_chat_data, headers=headers)
        if response.status_code == 200:
            chat_response = response.json()
            print(f"✓ Project-context chat successful: {chat_response['response'][:50]}...")
        else:
            print(f"✗ Project-context chat failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ Project-context chat request failed: {e}")

if __name__ == "__main__":
    print("🧪 Testing API Endpoints")
    print("Make sure the backend is running on http://localhost:8000")
    print()
    
    # Test authentication
    token = test_register_and_login()
    
    # Test token endpoints
    test_token_endpoints(token)
    
    # Test chat endpoint
    test_chat_endpoint(token)
    
    print("\n=== Test Complete ===")
