import { useState, useEffect, createContext, useContext } from 'react';
import axios from '../config/axios';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUser = async () => {
      if (!token) {
        setLoading(false);
        return;
      }
      try {
        const response = await axios.get('/api/users/me', {
          headers: { Authorization: `Bearer ${token}` }
        });
        setUser(response.data);
        setError(null);
      } catch (err) {
        localStorage.removeItem('token');
        setToken(null);
        setUser(null);
        setError(null);
      } finally {
        setLoading(false);
      }
    };
    fetchUser();
  }, [token]);

  const login = async (username, password) => {
    try {
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);
      formData.append('grant_type', 'password');
      const response = await axios.post('/api/token', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      const { access_token: newToken } = response.data;
      localStorage.setItem('token', newToken);
      setToken(newToken);
      // Fetch user data after successful login
      const userResponse = await axios.get('/api/users/me', {
        headers: { Authorization: `Bearer ${newToken}` }
      });
      setUser(userResponse.data);
      setError(null);
      toast.success('Login successful!');
      navigate('/dashboard');
      return true;
    } catch (err) {
      setError(err.response?.data?.detail || 'Login failed');
      toast.error(err.response?.data?.detail || 'Login failed');
      return false;
    }
  };

  const register = async (username, email, password, fullName) => {
    try {
      const userData = {
        username,
        email,
        password,
        full_name: fullName
      };
      await axios.post('/api/register', userData);
      toast.success('Registration successful! Please login.');
      navigate('/login');
      setError(null);
      return true;
    } catch (err) {
      setError(err.response?.data?.detail || 'Registration failed');
      toast.error(err.response?.data?.detail || 'Registration failed');
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    setToken(null);
    setUser(null);
    setError(null);
    navigate('/login');
    toast.success('Logged out successfully');
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await axios.put('/api/users/me', profileData);
      setUser(response.data);
      setError(null);
      toast.success('Profile updated successfully');
      return true;
    } catch (err) {
      setError(err.response?.data?.detail || 'Profile update failed');
      toast.error(err.response?.data?.detail || 'Profile update failed');
      return false;
    }
  };

  const value = {
    user,
    token,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 