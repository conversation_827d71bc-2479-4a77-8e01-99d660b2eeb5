# Copyright 2013 by Rackspace Hosting, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import sys

from falcon import bench


def fail(returncode, e):
    sys.stderr.write('\nERROR: %s\n\n' % e)
    sys.exit(returncode)


def main():
    try:
        bench.main()
    except KeyboardInterrupt:
        fail(1, 'Interrupted, terminating benchmark')
    except RuntimeError as e:
        fail(1, e)


if __name__ == '__main__':
    main()
