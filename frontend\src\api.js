import axios from './config/axios'

const API_BASE = "/api";

// Authentication
export const login = (username, password) => {
  const formData = new FormData()
  formData.append('username', username)
  formData.append('password', password)
  formData.append('grant_type', 'password')
  
  return axios.post('/api/token', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).then(res => res.data)
}

export const register = (userData) =>
  axios.post('/api/register', userData).then(res => res.data)

export const getCurrentUser = () =>
  axios.get('/api/users/me').then(res => res.data)

// User stats and projects
export const getUserStats = () =>
  axios.get('/api/users/stats').then(res => res.data)

export const getUserProjects = () =>
  axios.get('/api/users/projects').then(res => res.data)

// Code generation
export const generateCode = async (prompt) => {
    try {
        const response = await axios.post('/api/generate', { prompt });
        return response.data;
    } catch (error) {
        console.error('API Error:', error);

        // Handle token exhaustion error
        if (error.response?.status === 402) {
            const tokenError = new Error('Insufficient tokens');
            tokenError.tokenInfo = error.response.data;
            throw tokenError;
        }

        throw error;
    }
};

// Project management
export const createProject = (projectData) =>
  axios.post('/api/projects', projectData).then(res => res.data)

export const getProject = (id) =>
  axios.get(`/api/projects/${id}`).then(res => res.data)

export const updateProject = (id, projectData) =>
  axios.put(`/api/projects/${id}`, projectData).then(res => res.data)

export const deleteProject = (id) =>
  axios.delete(`/api/projects/${id}`).then(res => res.data)

// API documentation
export const getApiDocs = () =>
  axios.get('/api/api-docs').then(res => res.data)

// Project analysis
export const analyzeProject = (projectId) =>
  axios.post(`/api/projects/${projectId}/analyze`).then(res => res.data)

// API Generation
export const getProjects = () =>
  axios.get('/api/projects').then(res => res.data)

export const downloadProject = (id) =>
  axios.get(`/api/projects/${id}/download`, { responseType: 'blob' })

// Forum
export const getForumTopics = (params) =>
  axios.get('/api/forum/topics', { params }).then(res => res.data)

export const getForumTopic = (id) =>
  axios.get(`/api/forum/topics/${id}`).then(res => res.data)

export const createForumTopic = (data) =>
  axios.post('/api/forum/topics', data).then(res => res.data)

export const addForumReply = (topicId, data) =>
  axios.post(`/api/forum/topics/${topicId}/replies`, data).then(res => res.data)

export const likeTopic = (topicId) =>
  axios.post(`/api/forum/topics/${topicId}/like`).then(res => res.data)

// User Profile
export const updateUserProfile = (data) =>
  axios.put('/api/users/profile', data).then(res => res.data)

// Contact
export const submitContactForm = (formData) =>
  axios.post('/api/contact', formData).then(res => res.data)

export const chat = async (message, model = 'vikki', projectId = null, projectPath = null) => {
  try {
    const requestData = {
      message,
      model
    };

    // Add project context if provided
    if (projectId) {
      requestData.project_id = projectId;
    }
    if (projectPath) {
      requestData.project_path = projectPath;
    }

    const response = await axios.post('/api/chat', requestData);

    if (!response.data) {
      throw new Error('Failed to get chat response');
    }

    return response.data.response;
  } catch (error) {
    console.error('Chat error:', error);

    // Handle token exhaustion error
    if (error.response?.status === 402) {
      const tokenError = new Error('Insufficient tokens');
      tokenError.tokenInfo = error.response.data;
      throw tokenError;
    }

    throw error;
  }
};

// API Key management
export const getUserApiKey = () =>
  axios.get('/api/users/api-key').then(res => res.data)

export const regenerateUserApiKey = () =>
  axios.post('/api/users/api-key/regenerate').then(res => res.data)

// Payment API functions
export const getPlans = async () => {
  try {
    const response = await axios.get('/api/plans');
    return response.data;
  } catch (error) {
    console.error('Get plans error:', error);
    throw error;
  }
};

export const createPaymentOrder = async (planData) => {
  try {
    const response = await axios.post('/api/payment/create-order', planData);
    return response.data;
  } catch (error) {
    console.error('Create payment order error:', error);
    throw error;
  }
};

export const verifyPayment = async (paymentData) => {
  try {
    const response = await axios.post('/api/payment/verify', paymentData);
    return response.data;
  } catch (error) {
    console.error('Verify payment error:', error);
    throw error;
  }
};

export const getSubscriptionStatus = async () => {
  try {
    const response = await axios.get('/api/subscription/status');
    return response.data;
  } catch (error) {
    console.error('Get subscription status error:', error);
    throw error;
  }
};

export const cancelSubscription = async () => {
  try {
    const response = await axios.post('/api/subscription/cancel');
    return response.data;
  } catch (error) {
    console.error('Cancel subscription error:', error);
    throw error;
  }
};

export const refreshTokens = async () => {
  try {
    const response = await axios.post('/api/tokens/refresh');
    return response.data;
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};