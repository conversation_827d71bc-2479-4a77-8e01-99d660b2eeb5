#!/usr/bin/env python3
"""
Comprehensive test script to verify all fixes
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_authentication():
    """Test authentication endpoints"""
    print("=== Testing Authentication ===")
    
    # Test registration
    register_data = {
        "username": "testuser2",
        "email": "<EMAIL>", 
        "password": "testpass123",
        "full_name": "Test User 2"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/register", json=register_data)
        if response.status_code == 200:
            print("✓ Registration successful")
        elif response.status_code == 400 and "already registered" in response.text:
            print("✓ User already exists (expected)")
        else:
            print(f"✗ Registration failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"✗ Registration request failed: {e}")
        return None
    
    # Test login
    login_data = {
        "username": "testuser2",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/token", 
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        if response.status_code == 200:
            token_data = response.json()
            print("✓ Login successful")
            return token_data["access_token"]
        else:
            print(f"✗ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"✗ Login request failed: {e}")
        return None

def test_profile_endpoints(token):
    """Test profile update endpoints"""
    if not token:
        print("✗ No token available for profile testing")
        return
        
    print("\n=== Testing Profile Endpoints ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test profile update
    profile_data = {
        "name": "Updated Test User",
        "email": "<EMAIL>"
    }
    
    try:
        response = requests.put(f"{BASE_URL}/api/users/me", json=profile_data, headers=headers)
        if response.status_code == 200:
            user_data = response.json()
            print(f"✓ Profile update successful: {user_data['full_name']}")
        else:
            print(f"✗ Profile update failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ Profile update request failed: {e}")
    
    # Test subscription update
    subscription_data = {"plan": "pro"}
    
    try:
        response = requests.post(f"{BASE_URL}/api/users/subscription", json=subscription_data, headers=headers)
        if response.status_code == 200:
            sub_data = response.json()
            print(f"✓ Subscription update successful: {sub_data['plan']}")
        else:
            print(f"✗ Subscription update failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ Subscription update request failed: {e}")

def test_forum_endpoints():
    """Test forum endpoints"""
    print("\n=== Testing Forum Endpoints ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/forum/topics")
        if response.status_code == 200:
            topics = response.json()
            print(f"✓ Forum topics endpoint working: {len(topics)} topics")
        else:
            print(f"✗ Forum topics failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ Forum topics request failed: {e}")

def test_chat_endpoints(token):
    """Test chat endpoints"""
    if not token:
        print("✗ No token available for chat testing")
        return
        
    print("\n=== Testing Chat Endpoints ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test basic chat
    chat_data = {
        "message": "Hello, how are you?",
        "model": "basic"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/chat", json=chat_data, headers=headers)
        if response.status_code == 200:
            chat_response = response.json()
            print(f"✓ Basic chat successful: {chat_response['response'][:50]}...")
        else:
            print(f"✗ Basic chat failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ Basic chat request failed: {e}")
    
    # Test project-context chat
    project_chat_data = {
        "message": "Add a new component to this project",
        "model": "basic",
        "project_id": "test-project",
        "project_path": "/path/to/project"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/chat", json=project_chat_data, headers=headers)
        if response.status_code == 200:
            chat_response = response.json()
            print(f"✓ Project-context chat successful: {chat_response['response'][:50]}...")
        else:
            print(f"✗ Project-context chat failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ Project-context chat request failed: {e}")

def test_token_endpoints(token):
    """Test token endpoints"""
    if not token:
        print("✗ No token available for token testing")
        return
        
    print("\n=== Testing Token Endpoints ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/tokens/info", headers=headers)
        if response.status_code == 200:
            token_data = response.json()
            print(f"✓ Token info successful: {token_data['remaining_tokens']} tokens remaining")
        else:
            print(f"✗ Token info failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ Token info request failed: {e}")

if __name__ == "__main__":
    print("🧪 Comprehensive API Test Suite")
    print("Make sure the backend is running on http://localhost:8000")
    print()
    
    # Test authentication
    token = test_authentication()
    
    # Test profile endpoints
    test_profile_endpoints(token)
    
    # Test forum endpoints
    test_forum_endpoints()
    
    # Test chat endpoints
    test_chat_endpoints(token)
    
    # Test token endpoints
    test_token_endpoints(token)
    
    print("\n=== Test Complete ===")
    print("If all tests pass, the frontend issues should be resolved!")
