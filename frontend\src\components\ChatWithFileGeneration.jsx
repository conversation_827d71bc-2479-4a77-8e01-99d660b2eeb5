import React, { useState } from 'react';
import { FiSend, <PERSON>Loader, FiFile, FiFolder, FiDownload, FiCode } from 'react-icons/fi';
import { chat } from '../api';
import toast from 'react-hot-toast';
import UpgradeModal from './UpgradeModal';

const ChatWithFileGeneration = ({ projectId = null, projectPath = null }) => {
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [chatHistory, setChatHistory] = useState([]);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeError, setUpgradeError] = useState(null);

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!message.trim() || loading) return;

    const userMessage = message;
    setMessage('');
    setLoading(true);

    // Add user message to chat history
    setChatHistory(prev => [...prev, {
      type: 'user',
      content: userMessage,
      timestamp: new Date()
    }]);

    try {
      const response = await chat(userMessage, 'basic', projectId, projectPath);
      
      // Check if the response indicates files were generated
      const isFileGeneration = response.includes('**Project Generated Successfully!**') || 
                              response.includes('**Files Created:**');

      setChatHistory(prev => [...prev, {
        type: 'assistant',
        content: response,
        timestamp: new Date(),
        isFileGeneration
      }]);

      if (isFileGeneration) {
        toast.success('Files generated successfully!');
      }

    } catch (error) {
      console.error('Chat error:', error);
      
      // Handle token exhaustion error
      if (error.tokenInfo) {
        setUpgradeError(error.tokenInfo);
        setShowUpgradeModal(true);
        return;
      }

      setChatHistory(prev => [...prev, {
        type: 'error',
        content: error.message || 'Failed to send message',
        timestamp: new Date()
      }]);

      toast.error(error.message || 'Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  const renderMessage = (msg, index) => {
    const isUser = msg.type === 'user';
    const isError = msg.type === 'error';
    const isFileGeneration = msg.isFileGeneration;

    return (
      <div key={index} className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
        <div className={`max-w-[80%] rounded-2xl px-4 py-3 ${
          isUser 
            ? 'bg-gradient-to-r from-cyan-500 to-blue-600 text-white' 
            : isError
            ? 'bg-gradient-to-r from-red-500/20 to-red-600/20 text-red-300 border border-red-500/30'
            : isFileGeneration
            ? 'bg-gradient-to-r from-green-500/20 to-emerald-600/20 text-green-300 border border-green-500/30'
            : 'bg-gradient-to-r from-gray-700/50 to-gray-800/50 text-gray-200 border border-gray-600/30'
        }`}>
          {isFileGeneration ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <FiCode className="text-green-400" />
                <span className="font-semibold text-green-400">Files Generated</span>
              </div>
              <div className="whitespace-pre-wrap text-sm">{msg.content}</div>
            </div>
          ) : (
            <div className="whitespace-pre-wrap text-sm">{msg.content}</div>
          )}
          <div className={`text-xs mt-2 opacity-70 ${isUser ? 'text-cyan-100' : 'text-gray-400'}`}>
            {msg.timestamp.toLocaleTimeString()}
          </div>
        </div>
      </div>
    );
  };

  const suggestions = [
    "Create a React todo app with local storage",
    "Build a FastAPI REST API for a blog",
    "Generate a Next.js landing page with dark mode",
    "Create a Python data analysis script",
    "Build a Node.js Express server with authentication",
    "Generate a React component library",
    "Create a Django web application",
    "Build a Vue.js dashboard with charts"
  ];

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-[#181a20] via-[#232526] to-[#23243a] rounded-2xl">
      {/* Header */}
      <div className="p-4 border-b border-cyan-900/30 bg-gradient-to-r from-[#23243a]/80 to-[#181a20]/90 rounded-t-2xl">
        <h3 className="text-lg font-bold text-cyan-200 flex items-center gap-2">
          <FiCode className="text-cyan-400" />
          {projectId ? 'Project Chat' : 'AI Assistant'}
        </h3>
        <p className="text-sm text-gray-400 mt-1">
          {projectId 
            ? 'Chat about your current project or ask for modifications'
            : 'Ask me to create files, projects, or help with coding'
          }
        </p>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {chatHistory.length === 0 ? (
          <div className="text-center text-gray-400 mt-8">
            <FiCode className="text-4xl mx-auto mb-4 text-cyan-400/50" />
            <p className="text-lg font-semibold mb-2">Start a conversation</p>
            <p className="text-sm mb-6">Ask me to create projects, generate code, or help with development</p>
            
            {/* Suggestions */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-w-2xl mx-auto">
              {suggestions.slice(0, 4).map((suggestion, idx) => (
                <button
                  key={idx}
                  onClick={() => setMessage(suggestion)}
                  className="text-xs bg-gradient-to-r from-cyan-900/30 to-blue-900/30 text-cyan-200 px-3 py-2 rounded-lg border border-cyan-900/20 hover:bg-cyan-800/40 hover:border-cyan-400 transition-colors text-left"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        ) : (
          chatHistory.map(renderMessage)
        )}
        
        {loading && (
          <div className="flex justify-start mb-4">
            <div className="bg-gradient-to-r from-gray-700/50 to-gray-800/50 text-gray-200 border border-gray-600/30 rounded-2xl px-4 py-3">
              <div className="flex items-center gap-2">
                <FiLoader className="animate-spin text-cyan-400" />
                <span className="text-sm">Thinking...</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Input Form */}
      <form onSubmit={handleSendMessage} className="p-4 border-t border-cyan-900/30 bg-gradient-to-r from-[#23243a]/80 to-[#181a20]/90 rounded-b-2xl">
        <div className="flex gap-2">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder={projectId ? "Ask about this project..." : "Ask me to create files or projects..."}
            className="flex-1 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 text-cyan-100 placeholder:text-cyan-400/60 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-cyan-900/20"
            disabled={loading}
          />
          <button
            type="submit"
            disabled={loading || !message.trim()}
            className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold px-6 py-3 rounded-xl shadow-xl transition-all flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FiSend className="text-lg" />
          </button>
        </div>
      </form>

      {/* Upgrade Modal */}
      <UpgradeModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        error={upgradeError?.error || ''}
        remainingTokens={upgradeError?.remaining_tokens || 0}
        tokensNeeded={upgradeError?.tokens_needed || 0}
      />
    </div>
  );
};

export default ChatWithFileGeneration;
