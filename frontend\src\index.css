@tailwind base;
@tailwind components;
@tailwind utilities;

*[class*="bg-blue"],
*[class*="from-blue"], 
*[class*="to-blue"],
.bg-gradient-to-r.from-blue-600,
.bg-gradient-to-r.to-blue-600 {
  background-color: #111111 !important;
  background-image: none !important;
  background: #111111 !important;
}

/* Keep body and html black for dark theme */
html,
body,
#root {
  background-color: #938f8f !important;
  color: #ffffff !important;
}

/* Custom styles for the app */
body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background-color: #000000 !important;
  color: #ffffff !important;
  transition: background-color 0.3s ease, color 0.3s ease;
}

#root {
  width: 100%;
  min-height: 100vh;
  background-color: #000000 !important;
  color: #ffffff !important;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Force black theme for all elements */
* {
  border-color: #333333 !important;
}

/* Override ALL possible blue backgrounds */
*[class*="bg-blue"],
*[class*="from-blue"],
*[class*="to-blue"],
.bg-gradient-to-r,
.bg-gradient-to-br,
.bg-gradient-to-bl,
.bg-gradient-to-tr,
.bg-gradient-to-tl {
  background-color: #111111 !important;
  background-image: none !important;
  background: #111111 !important;
}

/* Black theme styles - but allow component styling */
.bg-white,
.bg-gray-50 {
  background-color: #000000 !important;
}

.bg-gray-100,
.bg-gray-200 {
  background-color: #111111 !important;
}

.bg-gray-800,
.bg-gray-900 {
  background-color: #111111 !important;
}

.text-gray-900,
.text-gray-800,
.text-gray-700,
.text-black {
  color: #ffffff !important;
}

.text-gray-600,
.text-gray-500 {
  color: #cccccc !important;
}

.text-gray-400 {
  color: #999999 !important;
}

.text-gray-300 {
  color: #bbbbbb !important;
}

/* Card and panel backgrounds */
.rounded-lg,
.rounded-xl,
.shadow,
.shadow-sm,
.shadow-lg {
  background-color: #111111 !important;
  border-color: #333333 !important;
  box-shadow: 0 4px 6px -1px rgba(255, 255, 255, 0.1), 0 2px 4px -1px rgba(255, 255, 255, 0.06) !important;
}

/* Form inputs */
input,
textarea,
select {
  background-color: #111111 !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

input::placeholder,
textarea::placeholder {
  color: #666666 !important;
}

input:focus,
textarea:focus,
select:focus {
  background-color: #111111 !important;
  border-color: #ffffff !important;
  color: #ffffff !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2) !important;
}

/* Navigation */
nav {
  background-color: #111111 !important;
  border-color: #333333 !important;
}

/* Buttons */
button {
  border-color: #333333 !important;
}

/* Status badges and indicators */
.bg-green-100 {
  background-color: #003300 !important;
}

.text-green-800 {
  color: #00ff00 !important;
}

.bg-red-100 {
  background-color: #330000 !important;
}

.text-red-600,
.text-red-800 {
  color: #ff4444 !important;
}

.bg-blue-100 {
  background-color: #000033 !important;
}

.text-blue-600,
.text-blue-800 {
  color: #4444ff !important;
}

.bg-yellow-100 {
  background-color: #333300 !important;
}

.text-yellow-600,
.text-yellow-800 {
  color: #ffff44 !important;
}

.bg-purple-100 {
  background-color: #330033 !important;
}

.text-purple-600,
.text-purple-800 {
  color: #ff44ff !important;
}

/* Hover states */
.hover\:bg-gray-50:hover,
.hover\:bg-gray-100:hover,
.hover\:bg-gray-200:hover {
  background-color: #222222 !important;
}

.hover\:bg-blue-50:hover {
  background-color: #001122 !important;
}

.hover\:text-gray-700:hover,
.hover\:text-gray-900:hover {
  color: #ffffff !important;
}

/* Gradients for black theme */
.bg-gradient-to-r {
  background: linear-gradient(to right, #111111, #222222) !important;
}

.from-purple-50.to-blue-50 {
  background: linear-gradient(to right, #111111, #111111) !important;
}

.from-yellow-50.to-orange-50 {
  background: linear-gradient(to right, #111111, #111111) !important;
}

.from-green-50.to-blue-50 {
  background: linear-gradient(to right, #111111, #111111) !important;
}

/* Fix all blue gradient backgrounds */
.bg-gradient-to-r.from-blue-600.to-purple-600,
.bg-gradient-to-r.from-purple-600.to-blue-600,
.bg-gradient-to-r.from-blue-600.to-cyan-600,
.bg-gradient-to-r.from-blue-50.to-cyan-50,
.bg-gradient-to-br.from-blue-50.to-indigo-100,
.bg-gradient-to-br.from-blue-500.to-purple-600 {
  background: linear-gradient(to right, #111111, #222222) !important;
}

/* Fix all blue backgrounds */
.bg-blue-50,
.bg-blue-100,
.bg-blue-500,
.bg-blue-600,
.bg-blue-700,
.bg-blue-800,
.bg-blue-900 {
  background-color: #111111 !important;
}

/* Fix hover states for blue backgrounds */
.hover\:bg-blue-50:hover,
.hover\:bg-blue-100:hover,
.hover\:bg-blue-600:hover,
.hover\:bg-blue-700:hover,
.hover\:from-blue-700:hover,
.hover\:to-blue-700:hover,
.hover\:from-purple-700:hover,
.hover\:to-purple-700:hover {
  background-color: #222222 !important;
  background: linear-gradient(to right, #222222, #333333) !important;
}

/* Borders */
.border-gray-200,
.border-gray-300,
.border-gray-400 {
  border-color: #333333 !important;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #111111 !important;
}

::-webkit-scrollbar-thumb {
  background: #333333 !important;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555555 !important;
  }

/* Focus states */
.focus\:ring-blue-500:focus,
.focus\:ring-purple-500:focus,
.focus\:ring-orange-500:focus {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3) !important;
}

.focus\:border-blue-500:focus,
.focus\:border-purple-500:focus,
.focus\:border-orange-500:focus {
  border-color: #ffffff !important;
}



/* Icons should be white */
svg {
  color: #ffffff !important;
}

/* Loading spinner */
.animate-spin {
  color: #ffffff !important;
}

/* Table styles */
table {
  background-color: #111111 !important;
}

th, td {
  border-color: #333333 !important;
  color: #ffffff !important;
}

/* Modal and dropdown backgrounds */
.dropdown-menu,
.modal {
  background-color: #111111 !important;
  border-color: #333333 !important;
}

/* Font size utilities */
.text-sm * {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  color: inherit !important;
}

.text-lg * {
  font-size: 1.125rem !important;
  line-height: 1.75rem !important;
}

/* Compact mode */
.compact-mode {
  --spacing-factor: 0.75;
}

.compact-mode .p-4 {
  padding: calc(1rem * var(--spacing-factor)) !important;
}

.compact-mode .p-6 {
  padding: calc(1.5rem * var(--spacing-factor)) !important;
}

.compact-mode .py-8 {
  padding-top: calc(2rem * var(--spacing-factor)) !important;
  padding-bottom: calc(2rem * var(--spacing-factor)) !important;
}

.compact-mode .space-y-4 > :not([hidden]) ~ :not([hidden]) {
  margin-top: calc(1rem * var(--spacing-factor)) !important;
}

.compact-mode .space-y-6 > :not([hidden]) ~ :not([hidden]) {
  margin-top: calc(1.5rem * var(--spacing-factor)) !important;
}

.compact-mode .space-y-8 > :not([hidden]) ~ :not([hidden]) {
  margin-top: calc(2rem * var(--spacing-factor)) !important;
}

.compact-mode .mb-4 {
  margin-bottom: calc(1rem * var(--spacing-factor)) !important;
}

.compact-mode .mb-6 {
  margin-bottom: calc(1.5rem * var(--spacing-factor)) !important;
}

.compact-mode .mb-8 {
  margin-bottom: calc(2rem * var(--spacing-factor)) !important;
}

/* Reduced motion support */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Loading states */
.loading {
  color: #ffffff !important;
}

.success {
  color: #00ff00 !important;
}

.error {
  color: #ff4444 !important;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  :root {
    --contrast-multiplier: 1.5;
  }
  
  .border {
    border-width: 2px !important;
  }
  
  .text-gray-600 {
    color: #ffffff !important;
}
}

/* Select option styles */
select option {
  background-color: #111111 !important;
  color: #ffffff !important;
  border: none !important;
}

select option:hover,
select option:focus {
  background-color: #333333 !important;
  color: #ffffff !important;
}

select option:checked {
    background-color: #ffffff !important;
  color: #000000 !important;
  }
  
/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .compact-mode {
    --spacing-factor: 0.5;
  }
}
