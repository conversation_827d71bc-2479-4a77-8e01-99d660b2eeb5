"""
Advanced Project Analyzer for VIKKI AI
Handles ZIP file uploads, analysis, and recommendations
"""
import os
import zipfile
import tempfile
import shutil
import json
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import chardet
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ProjectAnalyzer:
    def __init__(self):
        self.supported_extensions = {
            '.py': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.json': 'json',
            '.md': 'markdown',
            '.txt': 'text',
            '.yml': 'yaml',
            '.yaml': 'yaml',
            '.xml': 'xml',
            '.php': 'php',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.go': 'go',
            '.rs': 'rust',
            '.rb': 'ruby',
            '.vue': 'vue',
            '.svelte': 'svelte'
        }
        
        self.framework_indicators = {
            'react': ['package.json', 'src/App.js', 'src/App.jsx', 'public/index.html'],
            'nextjs': ['next.config.js', 'pages/', 'app/'],
            'vue': ['vue.config.js', 'src/main.js', 'src/App.vue'],
            'angular': ['angular.json', 'src/app/', 'src/main.ts'],
            'svelte': ['svelte.config.js', 'src/App.svelte'],
            'django': ['manage.py', 'settings.py', 'urls.py'],
            'flask': ['app.py', 'requirements.txt'],
            'fastapi': ['main.py', 'requirements.txt'],
            'express': ['package.json', 'server.js', 'app.js'],
            'spring': ['pom.xml', 'src/main/java/'],
            'laravel': ['composer.json', 'artisan', 'app/Http/']
        }

    async def extract_and_analyze_zip(self, zip_file_path: str, user_id: str) -> Dict:
        """Extract ZIP file and analyze the project"""
        try:
            # Create temporary directory for extraction
            temp_dir = tempfile.mkdtemp(prefix=f"project_analysis_{user_id}_")
            
            # Extract ZIP file
            with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # Analyze the extracted project
            analysis_result = await self.analyze_project_structure(temp_dir)
            
            # Store the project for later use
            project_id = self.store_analyzed_project(temp_dir, user_id, analysis_result)
            analysis_result['project_id'] = project_id
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing ZIP file: {e}")
            raise Exception(f"Failed to analyze project: {str(e)}")
        finally:
            # Clean up temporary directory
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)

    async def analyze_project_structure(self, project_path: str) -> Dict:
        """Analyze project structure and provide recommendations"""
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'project_structure': {},
            'detected_frameworks': [],
            'languages': {},
            'file_count': 0,
            'total_size': 0,
            'recommendations': [],
            'improvements': [],
            'security_issues': [],
            'performance_suggestions': [],
            'code_quality': {},
            'dependencies': {},
            'missing_files': []
        }
        
        # Walk through project directory
        for root, dirs, files in os.walk(project_path):
            # Skip hidden directories and common ignore patterns
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv', 'env']]
            
            for file in files:
                if file.startswith('.'):
                    continue
                    
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, project_path)
                
                # Get file info
                file_info = self.analyze_file(file_path)
                analysis['project_structure'][relative_path] = file_info
                
                # Update statistics
                analysis['file_count'] += 1
                analysis['total_size'] += file_info.get('size', 0)
                
                # Count languages
                language = file_info.get('language')
                if language:
                    analysis['languages'][language] = analysis['languages'].get(language, 0) + 1

        # Detect frameworks
        analysis['detected_frameworks'] = self.detect_frameworks(analysis['project_structure'])
        
        # Analyze dependencies
        analysis['dependencies'] = self.analyze_dependencies(analysis['project_structure'])
        
        # Generate recommendations
        analysis['recommendations'] = self.generate_recommendations(analysis)
        analysis['improvements'] = self.generate_improvements(analysis)
        analysis['security_issues'] = self.check_security_issues(analysis)
        analysis['performance_suggestions'] = self.generate_performance_suggestions(analysis)
        analysis['missing_files'] = self.check_missing_files(analysis)
        
        # Calculate code quality score
        analysis['code_quality'] = self.calculate_code_quality(analysis)
        
        return analysis

    def analyze_file(self, file_path: str) -> Dict:
        """Analyze individual file"""
        try:
            file_stat = os.stat(file_path)
            file_ext = Path(file_path).suffix.lower()
            
            file_info = {
                'size': file_stat.st_size,
                'extension': file_ext,
                'language': self.supported_extensions.get(file_ext, 'unknown'),
                'mime_type': mimetypes.guess_type(file_path)[0],
                'lines': 0,
                'encoding': 'unknown'
            }
            
            # Try to read file content for text files
            if file_info['language'] != 'unknown' and file_stat.st_size < 1024 * 1024:  # Max 1MB
                try:
                    with open(file_path, 'rb') as f:
                        raw_data = f.read()
                        
                    # Detect encoding
                    encoding_result = chardet.detect(raw_data)
                    file_info['encoding'] = encoding_result.get('encoding', 'unknown')
                    
                    # Count lines for text files
                    if encoding_result.get('confidence', 0) > 0.7:
                        try:
                            content = raw_data.decode(file_info['encoding'])
                            file_info['lines'] = len(content.splitlines())
                        except:
                            pass
                            
                except Exception as e:
                    logger.warning(f"Could not read file {file_path}: {e}")
            
            return file_info
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return {'size': 0, 'extension': '', 'language': 'unknown'}

    def detect_frameworks(self, project_structure: Dict) -> List[str]:
        """Detect frameworks used in the project"""
        detected = []
        files = list(project_structure.keys())
        
        for framework, indicators in self.framework_indicators.items():
            score = 0
            for indicator in indicators:
                if indicator.endswith('/'):
                    # Directory indicator
                    if any(f.startswith(indicator) for f in files):
                        score += 1
                else:
                    # File indicator
                    if indicator in files or any(f.endswith(indicator) for f in files):
                        score += 1
            
            # If more than half indicators are present, consider framework detected
            if score >= len(indicators) * 0.5:
                detected.append(framework)
        
        return detected

    def analyze_dependencies(self, project_structure: Dict) -> Dict:
        """Analyze project dependencies"""
        dependencies = {
            'package_managers': [],
            'packages': {},
            'outdated_warning': False
        }
        
        # Check for different package manager files
        if 'package.json' in project_structure:
            dependencies['package_managers'].append('npm')
        if 'requirements.txt' in project_structure:
            dependencies['package_managers'].append('pip')
        if 'composer.json' in project_structure:
            dependencies['package_managers'].append('composer')
        if 'pom.xml' in project_structure:
            dependencies['package_managers'].append('maven')
        if 'Cargo.toml' in project_structure:
            dependencies['package_managers'].append('cargo')
        
        return dependencies

    def generate_recommendations(self, analysis: Dict) -> List[str]:
        """Generate general recommendations"""
        recommendations = []
        
        # Framework-specific recommendations
        frameworks = analysis['detected_frameworks']
        
        if 'react' in frameworks:
            recommendations.append("Consider using React.memo for performance optimization")
            recommendations.append("Implement proper error boundaries")
            recommendations.append("Use React DevTools for debugging")
        
        if 'nextjs' in frameworks:
            recommendations.append("Utilize Next.js Image optimization")
            recommendations.append("Implement proper SEO with next/head")
            recommendations.append("Use getStaticProps/getServerSideProps appropriately")
        
        if 'django' in frameworks:
            recommendations.append("Use Django's built-in security features")
            recommendations.append("Implement proper database migrations")
            recommendations.append("Use Django REST framework for APIs")
        
        # General recommendations based on file count
        if analysis['file_count'] > 100:
            recommendations.append("Consider modularizing your codebase")
            recommendations.append("Implement proper code organization")
        
        # Language-specific recommendations
        languages = analysis['languages']
        if 'javascript' in languages or 'typescript' in languages:
            recommendations.append("Use ESLint for code quality")
            recommendations.append("Implement proper testing with Jest")
        
        if 'python' in languages:
            recommendations.append("Use Black for code formatting")
            recommendations.append("Implement type hints for better code quality")
        
        return recommendations

    def generate_improvements(self, analysis: Dict) -> List[str]:
        """Generate improvement suggestions"""
        improvements = []
        
        # Check for missing important files
        files = list(analysis['project_structure'].keys())
        
        if not any('README' in f.upper() for f in files):
            improvements.append("Add a comprehensive README.md file")
        
        if not any('.gitignore' in f for f in files):
            improvements.append("Add .gitignore file to exclude unnecessary files")
        
        if 'javascript' in analysis['languages'] and 'package.json' not in files:
            improvements.append("Add package.json for dependency management")
        
        if 'python' in analysis['languages'] and 'requirements.txt' not in files:
            improvements.append("Add requirements.txt for Python dependencies")
        
        # Code organization improvements
        if analysis['file_count'] > 50 and not any('src/' in f for f in files):
            improvements.append("Organize code into src/ directory")
        
        return improvements

    def check_security_issues(self, analysis: Dict) -> List[str]:
        """Check for potential security issues"""
        security_issues = []
        files = list(analysis['project_structure'].keys())
        
        # Check for exposed sensitive files
        sensitive_patterns = ['.env', 'config.json', 'secrets', 'private_key', 'id_rsa']
        for pattern in sensitive_patterns:
            if any(pattern in f.lower() for f in files):
                security_issues.append(f"Potential sensitive file detected: {pattern}")
        
        # Framework-specific security checks
        if 'django' in analysis['detected_frameworks']:
            if not any('settings' in f for f in files):
                security_issues.append("Django settings file not found - ensure proper configuration")
        
        return security_issues

    def generate_performance_suggestions(self, analysis: Dict) -> List[str]:
        """Generate performance improvement suggestions"""
        suggestions = []
        
        # Large file warnings
        large_files = [f for f, info in analysis['project_structure'].items() 
                      if info.get('size', 0) > 1024 * 1024]  # > 1MB
        
        if large_files:
            suggestions.append(f"Consider optimizing large files: {', '.join(large_files[:3])}")
        
        # Framework-specific performance suggestions
        frameworks = analysis['detected_frameworks']
        
        if 'react' in frameworks:
            suggestions.append("Implement code splitting with React.lazy()")
            suggestions.append("Use React.memo for expensive components")
        
        if 'nextjs' in frameworks:
            suggestions.append("Optimize images with next/image")
            suggestions.append("Use dynamic imports for better performance")
        
        return suggestions

    def check_missing_files(self, analysis: Dict) -> List[str]:
        """Check for commonly missing files"""
        missing = []
        files = list(analysis['project_structure'].keys())
        
        # Common missing files
        if not any('license' in f.lower() for f in files):
            missing.append("LICENSE file")
        
        if not any('contributing' in f.lower() for f in files):
            missing.append("CONTRIBUTING.md file")
        
        if 'javascript' in analysis['languages']:
            if '.eslintrc' not in files and not any('eslint' in f for f in files):
                missing.append("ESLint configuration")
        
        return missing

    def calculate_code_quality(self, analysis: Dict) -> Dict:
        """Calculate overall code quality score"""
        score = 100
        issues = 0
        
        # Deduct points for issues
        issues += len(analysis['security_issues']) * 10
        issues += len(analysis['missing_files']) * 5
        
        # Add points for good practices
        if any('test' in f.lower() for f in analysis['project_structure'].keys()):
            score += 10
        
        if any('readme' in f.lower() for f in analysis['project_structure'].keys()):
            score += 5
        
        final_score = max(0, min(100, score - issues))
        
        return {
            'score': final_score,
            'grade': 'A' if final_score >= 90 else 'B' if final_score >= 80 else 'C' if final_score >= 70 else 'D',
            'issues_count': len(analysis['security_issues']) + len(analysis['missing_files']),
            'recommendations_count': len(analysis['recommendations'])
        }

    def store_analyzed_project(self, temp_dir: str, user_id: str, analysis: Dict) -> str:
        """Store analyzed project for later use"""
        try:
            # Create unique project ID
            project_id = f"analyzed_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Create storage directory
            storage_dir = Path("analyzed_projects") / project_id
            storage_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy project files
            shutil.copytree(temp_dir, storage_dir / "source", dirs_exist_ok=True)
            
            # Save analysis results
            with open(storage_dir / "analysis.json", 'w') as f:
                json.dump(analysis, f, indent=2, default=str)
            
            return project_id
            
        except Exception as e:
            logger.error(f"Error storing analyzed project: {e}")
            raise Exception(f"Failed to store project: {str(e)}")

    def get_project_files(self, project_id: str) -> Dict:
        """Get project files for chat context"""
        try:
            project_dir = Path("analyzed_projects") / project_id / "source"
            if not project_dir.exists():
                raise Exception("Project not found")
            
            files_content = {}
            for root, dirs, files in os.walk(project_dir):
                # Skip hidden directories
                dirs[:] = [d for d in dirs if not d.startswith('.')]
                
                for file in files:
                    if file.startswith('.'):
                        continue
                        
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, project_dir)
                    
                    # Only include text files under 100KB
                    if os.path.getsize(file_path) < 100 * 1024:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                files_content[relative_path] = f.read()
                        except:
                            # Skip binary or unreadable files
                            continue
            
            return files_content
            
        except Exception as e:
            logger.error(f"Error getting project files: {e}")
            return {}
