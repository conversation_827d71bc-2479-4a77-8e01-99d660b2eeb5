import { FiCode, FiUsers, FiServer, <PERSON>Award, <PERSON>Zap, FiShield } from 'react-icons/fi'
import PageContainer from '../components/layout/PageContainer'

const stats = [
  { name: 'Projects Generated', value: '10K+', icon: FiCode },
  { name: 'Happy Developers', value: '5K+', icon: FiUsers },
  { name: 'Server Uptime', value: '99.9%', icon: FiServer },
  { name: 'AI Models', value: '15+', icon: FiA<PERSON> },
]

const features = [
  {
    icon: FiCode,
    title: "AI-Powered Code Generation",
    description: "Generate complete full-stack applications with just a simple prompt using our advanced AI models."
  },
  {
    icon: FiZap,
    title: "Lightning Fast",
    description: "Get your projects ready in minutes, not hours. Our optimized AI generates production-ready code instantly."
  },
  {
    icon: FiShield,
    title: "Secure & Reliable",
    description: "Built with security in mind. All generated code follows best practices and security standards."
  }
]

const team = [
  {
    name: 'Dr<PERSON> <PERSON><PERSON><PERSON>',
    role: 'CEO & AI Research Lead',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    bio: 'Former AI researcher at OpenAI with 15+ years of experience in machine learning and code generation.',
  },
  {
    name: 'Priya Sharma',
    role: 'CTO & Lead Engineer',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    bio: 'Full-stack expert with a Ph.D. in Computer Science, specializing in AI-powered development tools.',
  },
  {
    name: 'Arjun Kumar',
    role: 'Head of Product',
    image: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    bio: 'Former product lead at Microsoft with expertise in developer experience and AI integration.',
  },
  {
    name: 'Ananya Singh',
    role: 'AI Model Specialist',
    image: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    bio: 'Machine learning engineer focused on optimizing AI models for code generation and analysis.',
  },
]

export default function About() {
  return (
    <PageContainer>
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] text-white font-sans pt-20" style={{ fontFamily: 'Inter, Space Grotesk, sans-serif' }}>
        {/* Hero Section */}
        <div className="relative overflow-hidden py-20 bg-gradient-to-br from-[#23243a]/90 to-[#181a20]/90 shadow-2xl rounded-b-3xl mb-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row items-center gap-16">
            <div className="flex-1">
              <h1 className="text-5xl md:text-7xl font-bold tracking-tight bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 text-transparent bg-clip-text drop-shadow-glow mb-6">
                About VIKKI AI
              </h1>
              <p className="text-xl md:text-2xl text-cyan-200/80 font-medium mb-6 leading-relaxed">
                Revolutionizing software development with AI-powered code generation.
                Transform your ideas into production-ready applications in minutes.
              </p>
              <p className="text-lg text-gray-300 leading-relaxed">
                VIKKI AI was founded in 2024 by KeyMatrix Solutions with a mission to democratize software development
                by making it accessible to everyone, regardless of their coding experience.
              </p>
            </div>
            <div className="flex-1 flex justify-center">
              <img
                className="w-full max-w-md rounded-3xl shadow-2xl ring-2 ring-cyan-400/30"
                src="https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=2850&q=80"
                alt="AI Development"
              />
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Why Choose VIKKI AI?
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                We're not just another code generator. We're your AI-powered development partner.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div key={index} className="group">
                    <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 h-full hover:border-cyan-500/50 transition-all duration-300 hover:transform hover:scale-105">
                      <div className="mb-6">
                        <Icon className="text-4xl text-cyan-400" />
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-4">
                        {feature.title}
                      </h3>
                      <p className="text-gray-300 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      {/* Stats Section */}
      <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 py-16 rounded-3xl mb-12 shadow-xl">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold text-cyan-100 tracking-wide mb-4">Trusted by developers worldwide</h2>
            <p className="mt-3 text-xl text-cyan-200/80 font-medium mb-8">Our platform has helped thousands of developers create and deploy APIs quickly and efficiently.</p>
          </div>
          <div className="mt-10 pb-12 sm:pb-16">
            <div className="relative">
              <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="mx-auto max-w-4xl grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 rounded-3xl bg-gradient-to-br from-[#23243a]/60 to-[#181a20]/60 shadow-xl p-8">
                  {stats.map((stat, index) => {
                    const Icon = stat.icon
                    return (
                      <div
                        key={stat.name}
                        className="flex flex-col items-center text-center gap-2 border-b border-cyan-900/20 sm:border-0 sm:border-r last:border-0 p-4"
                      >
                        <Icon className="mx-auto h-10 w-10 text-cyan-400 animate-pulse drop-shadow-glow" />
                        <dt className="mt-2 text-lg font-medium text-cyan-200/80">{stat.name}</dt>
                        <dd className="mt-1 text-3xl font-extrabold text-white drop-shadow-glow">{stat.value}</dd>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </PageContainer>
  )
}