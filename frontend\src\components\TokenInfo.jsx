import React from 'react';
import { useTokens } from '../hooks/useTokens';
import { FiRefreshCw, FiInfo } from 'react-icons/fi';
// import { format } from 'date-fns';

const TokenInfo = () => {
  const { tokenInfo, loading, error, refreshTokens } = useTokens();

  if (loading) {
    return (
      <div className="animate-pulse p-4 bg-gray-800 rounded-lg">
        <div className="h-4 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-700 rounded w-1/2"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg text-red-500">
        <p>{error}</p>
      </div>
    );
  }

  if (!tokenInfo) {
    return null;
  }

  const {
    total_tokens,
    used_tokens,
    remaining_tokens,
    last_refresh,
    usage_history,
    can_refresh,
    next_refresh_available,
    plan
  } = tokenInfo;

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-white">Token Usage</h3>
        <button
          onClick={refreshTokens}
          disabled={!can_refresh}
          className={`flex items-center gap-2 px-3 py-1 rounded-md text-sm transition-colors ${
            can_refresh
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
          }`}
          title={!can_refresh ? `Next refresh available in ${next_refresh_available}` : 'Refresh tokens'}
        >
          <FiRefreshCw className="w-4 h-4" />
          {can_refresh ? 'Refresh' : 'Refresh'}
        </button>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-700/50 p-3 rounded-lg">
          <p className="text-sm text-gray-400">Total Tokens</p>
          <p className="text-xl font-semibold text-white">{total_tokens}</p>
          <p className="text-xs text-gray-500 capitalize">{plan || 'free'} plan</p>
        </div>
        <div className="bg-gray-700/50 p-3 rounded-lg">
          <p className="text-sm text-gray-400">Used Tokens</p>
          <p className="text-xl font-semibold text-white">{used_tokens}</p>
          <p className="text-xs text-gray-500">{Math.round((used_tokens / total_tokens) * 100)}% used</p>
        </div>
        <div className="bg-gray-700/50 p-3 rounded-lg">
          <p className="text-sm text-gray-400">Remaining</p>
          <p className={`text-xl font-semibold ${remaining_tokens > 20 ? 'text-green-400' : remaining_tokens > 5 ? 'text-yellow-400' : 'text-red-400'}`}>
            {remaining_tokens}
          </p>
          <p className="text-xs text-gray-500">{Math.round((remaining_tokens / total_tokens) * 100)}% left</p>
        </div>
      </div>

      <div className="mb-4">
        <p className="text-sm text-gray-400">
          Last refresh: {new Date(last_refresh).toLocaleDateString()}
        </p>
        {!can_refresh && next_refresh_available && (
          <p className="text-sm text-yellow-400 mt-1">
            Next refresh available in: {next_refresh_available}
          </p>
        )}
        {can_refresh && (
          <p className="text-sm text-green-400 mt-1">
            ✓ Refresh available now
          </p>
        )}
      </div>

      <div>
        <h4 className="text-sm font-semibold text-gray-400 mb-2">Recent Usage</h4>
        <div className="space-y-2">
          {usage_history.slice(0, 5).map((usage, index) => (
            <div key={index} className="flex items-center justify-between bg-gray-700/30 p-2 rounded">
              <div className="flex items-center gap-2">
                <FiInfo className="w-4 h-4 text-blue-400" />
                <div>
                  <p className="text-sm text-white">{usage.feature}</p>
                  <p className="text-xs text-gray-400">{usage.description}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-white">{usage.tokens_used} tokens</p>
                <p className="text-xs text-gray-400">
                  {new Date(usage.timestamp).toLocaleDateString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TokenInfo; 