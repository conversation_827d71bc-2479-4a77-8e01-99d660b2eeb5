from typing import Optional, List, Dict, Literal
from pydantic import BaseModel
from datetime import datetime

class PromptRequest(BaseModel):
    prompt: str
    model: Literal["vikki", "vikki_advanced"] = "vikki"

class ChatRequest(BaseModel):
    message: str
    model: Literal["vikki", "vikki_advanced"] = "vikki"

class ChatResponse(BaseModel):
    response: str

class FileInfo(BaseModel):
    filename: str
    content: str

class Project(BaseModel):
    id: str
    name: str
    description: str
    created_at: datetime
    files: List[str]
    user_id: str
    file_contents: Optional[Dict[str, str]] = None

class TokenUsage(BaseModel):
    user_id: str
    feature: str
    tokens_used: int
    timestamp: datetime = datetime.utcnow()
    model: str = "vikki"
    description: Optional[str] = None

class TokenInfo(BaseModel):
    total_tokens: int
    used_tokens: int
    remaining_tokens: int
    last_refresh: datetime
    usage_history: List[TokenUsage] 