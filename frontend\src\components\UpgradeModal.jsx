import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FiX, FiZap, FiArrowRight } from 'react-icons/fi';

const UpgradeModal = ({ isOpen, onClose, error, remainingTokens, tokensNeeded }) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleUpgrade = () => {
    onClose();
    navigate('/pricing');
  };

  const getFeatureName = (error) => {
    if (error.includes('project_generation') || error.includes('50 tokens')) {
      return 'Project Generation';
    } else if (error.includes('chat') || error.includes('10 tokens')) {
      return 'Chat';
    }
    return 'This Feature';
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-gradient-to-br from-[#1a1b2e] to-[#16213e] rounded-2xl p-8 max-w-md w-full mx-4 border border-cyan-500/20 shadow-2xl">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
              <FiZap className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">Tokens Exhausted</h3>
              <p className="text-gray-400 text-sm">Upgrade to continue</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FiX className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="mb-6">
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4">
            <p className="text-red-400 text-sm font-medium">
              Insufficient tokens for {getFeatureName(error)}
            </p>
            <div className="mt-2 text-gray-300 text-sm">
              <div className="flex justify-between">
                <span>Tokens needed:</span>
                <span className="font-semibold">{tokensNeeded}</span>
              </div>
              <div className="flex justify-between">
                <span>Tokens remaining:</span>
                <span className="font-semibold text-red-400">{remainingTokens}</span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="text-white font-semibold">What you can do:</h4>
            <div className="space-y-2 text-sm text-gray-300">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                <span>Upgrade to Pro plan for 500 tokens</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                <span>Wait 24 hours for token refresh</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                <span>Upgrade to Enterprise for unlimited tokens</span>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            Wait for Refresh
          </button>
          <button
            onClick={handleUpgrade}
            className="flex-1 px-4 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white rounded-lg transition-all flex items-center justify-center gap-2 font-semibold"
          >
            Upgrade Now
            <FiArrowRight className="w-4 h-4" />
          </button>
        </div>

        {/* Footer */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            Upgrade now and get instant access to more tokens
          </p>
        </div>
      </div>
    </div>
  );
};

export default UpgradeModal;
