import React, { useEffect, useRef } from 'react';

const HCaptcha = ({ onVerify, onError, onExpire, theme = 'dark', size = 'normal' }) => {
  const captchaRef = useRef(null);
  const widgetId = useRef(null);
  
  const siteKey = 'e1f67dd6-6e08-4a28-afc0-cb087ebe7b8a';

  useEffect(() => {
    if (window.hcaptcha && captchaRef.current) {
      // Render hCaptcha
      widgetId.current = window.hcaptcha.render(captchaRef.current, {
        sitekey: siteKey,
        theme: theme,
        size: size,
        callback: (token) => {
          if (onVerify) {
            onVerify(token);
          }
        },
        'error-callback': (error) => {
          if (onError) {
            onError(error);
          }
        },
        'expired-callback': () => {
          if (onExpire) {
            onExpire();
          }
        }
      });
    }

    // Cleanup function
    return () => {
      if (window.hcaptcha && widgetId.current !== null) {
        window.hcaptcha.remove(widgetId.current);
      }
    };
  }, [onVerify, onError, onExpire, theme, size]);

  const reset = () => {
    if (window.hcaptcha && widgetId.current !== null) {
      window.hcaptcha.reset(widgetId.current);
    }
  };

  const execute = () => {
    if (window.hcaptcha && widgetId.current !== null) {
      window.hcaptcha.execute(widgetId.current);
    }
  };

  // Expose reset and execute methods
  React.useImperativeHandle(captchaRef, () => ({
    reset,
    execute
  }));

  return <div ref={captchaRef} className="h-captcha"></div>;
};

export default HCaptcha;
