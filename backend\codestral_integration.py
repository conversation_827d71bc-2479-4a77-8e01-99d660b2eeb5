from llama_cpp import <PERSON>lama
import time
from typing import Dict, List, Generator
import os
from pathlib import Path
import json
from datetime import datetime
import shutil
import uuid
import logging
import re
import asyncio

logger = logging.getLogger(__name__)

# Model paths mapping
MODEL_PATHS = {
    "vikki": "models/mistral-7b-instruct-v0.2.Q5_K_M.gguf",
    "vikki_advanced": "models/Codestral-22B-v0.1-Q4_K_M.gguf"
}

# Initialize models
models = {}

# Try to load VIKKI AI (Mistral) model
try:
    models["vikki"] = Llama(
        model_path=MODEL_PATHS["vikki"],
        n_ctx=1500,
        n_threads=8
    )
    logger.info("Successfully loaded VIKKI AI model")
except Exception as e:
    logger.error(f"Failed to load VIKKI AI model: {str(e)}")
    raise RuntimeError("Failed to load VIKKI AI model - this is required")

# Try to load VIKKI AI Advanced (Codestral) model
try:
    models["vikki_advanced"] = <PERSON>lama(
        model_path=MODEL_PATHS["vikki_advanced"],
        n_ctx=1500,
        n_threads=8,
        n_gpu_layers=0  # Force CPU only to reduce memory usage
    )
    logger.info("Successfully loaded VIKKI AI Advanced model")
except Exception as e:
    logger.warning(f"Failed to load VIKKI AI Advanced model: {str(e)}")
    logger.warning("VIKKI AI Advanced features will be disabled")
    # Don't raise error, just log warning

def generate_code_streaming(prompt: str, model_type: str = "vikki", user_id: str = None) -> Generator[Dict, None, None]:
    """Generate code with streaming updates and live progress"""
    try:
        # Map 'basic' and 'advanced' to the correct model keys
        if model_type == "basic":
            model_type = "vikki"
        elif model_type == "advanced":
            model_type = "vikki_advanced"
        # Validate model type
        if model_type not in models:
            if model_type == "vikki_advanced":
                yield {
                    "type": "error",
                    "data": {
                        "error": "VIKKI AI Advanced model is not available. Please use VIKKI AI instead."
                    }
                }
                return
            else:
                yield {
                    "type": "error",
                    "data": {
                        "error": f"Invalid model type: {model_type}"
                    }
                }
                return

        project_id = str(uuid.uuid4())
        project_dir = Path("generated_projects") / project_id
        project_dir.mkdir(parents=True, exist_ok=True)

        structure_prompt = f"""<s>[INST]
Create a complete, production-ready full-stack application for: {prompt}

REQUIREMENTS:
- Frontend: React with TypeScript, Vite, Tailwind CSS
- Backend: FastAPI with Python
- Database: SQLite with SQLAlchemy
- Authentication: JWT tokens
- Complete file structure with all necessary files

MANDATORY FILES TO INCLUDE:

BACKEND (backend/ folder):
- main.py (FastAPI app with all endpoints)
- requirements.txt (all dependencies)
- models.py (database models)
- auth.py (authentication logic)
- database.py (database connection)
- .env (environment variables)
- Dockerfile

FRONTEND (frontend/ folder):
- package.json (with all dependencies)
- vite.config.ts
- tailwind.config.js
- postcss.config.js
- tsconfig.json
- index.html
- src/main.tsx
- src/App.tsx
- src/index.css
- src/components/ (at least 3 components)
- src/pages/ (at least 2 pages)
- src/hooks/ (custom hooks)
- src/utils/ (utility functions)
- src/types/ (TypeScript types)

ROOT:
- README.md (comprehensive documentation)
- docker-compose.yml
- .gitignore

Return in this EXACT format:
// FILE: filepath
// CONTENT:
file content here
// END FILE

IMPORTANT: Generate COMPLETE, FUNCTIONAL code for each file. No placeholders or comments like "// Add more code here". Every file must be production-ready.
[/INST]</s>"""

        yield {
            "type": "status",
            "data": {
                "status": "generating",
                "message": "Generating project structure..."
            }
        }

        llm = models[model_type]
        structure = llm(structure_prompt, max_tokens=8192, temperature=0.1, stop=["</s>"])
        print("Model output:", structure)  # Debug print

        if not structure or "choices" not in structure:
            raise ValueError("Failed to generate project structure (no choices in model output)")

        if not structure["choices"] or "text" not in structure["choices"][0]:
            raise ValueError("Model output missing 'text' in choices")

        files = parse_project_files(structure["choices"][0]["text"])
        if not isinstance(files, list) or not files:
            raise ValueError("No files were generated or files list is invalid")

        total_files = len(files)
        for index, file_info in enumerate(files, 1):
            file_path = project_dir / file_info["path"]
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(file_info["content"])

            yield {
                "type": "file",
                "data": {
                    "file": file_info,
                    "progress": {
                        "current": index,
                        "total": total_files,
                        "percentage": (index / total_files) * 100
                    }
                }
            }

        project_info = {
            "id": project_id,
            "name": f"Project {project_id[:8]}",
            "description": prompt[:100] + "..." if len(prompt) > 100 else prompt,
            "created_at": datetime.utcnow().isoformat(),
            "files": [f["path"] for f in files],
            "user_id": user_id or ""
        }

        with open(project_dir / "project_info.json", "w") as f:
            json.dump(project_info, f)

        yield {
            "type": "complete",
            "data": {
                "project": {
                    "id": project_id,
                    "files": files
                }
            }
        }

    except Exception as e:
        logger.error(f"Error generating code: {str(e)}")
        yield {
            "type": "error",
            "data": {
                "error": str(e)
            }
        }

def generate_code(prompt: str, model_type: str = "vikki", user_id: str = None) -> Dict:
    """Generate full code with selected model"""
    try:
        # Map 'basic' and 'advanced' to the correct model keys
        if model_type == "basic":
            model_type = "vikki"
        elif model_type == "advanced":
            model_type = "vikki_advanced"
        # Validate model type
        if model_type not in models:
            if model_type == "vikki_advanced":
                raise ValueError("VIKKI AI Advanced model is not available. Please use VIKKI AI instead.")
            else:
                raise ValueError(f"Invalid model type: {model_type}")

        project_id = str(uuid.uuid4())
        project_dir = Path("generated_projects") / project_id
        project_dir.mkdir(parents=True, exist_ok=True)

        structure_prompt = f"""<s>[INST]
Create a complete, production-ready full-stack application for: {prompt}

REQUIREMENTS:
- Frontend: React with TypeScript, Vite, Tailwind CSS
- Backend: FastAPI with Python
- Database: SQLite with SQLAlchemy
- Authentication: JWT tokens
- Complete file structure with all necessary files

MANDATORY FILES TO INCLUDE:

BACKEND (backend/ folder):
- main.py (FastAPI app with all endpoints)
- requirements.txt (all dependencies)
- models.py (database models)
- auth.py (authentication logic)
- database.py (database connection)
- .env (environment variables)
- Dockerfile

FRONTEND (frontend/ folder):
- package.json (with all dependencies)
- vite.config.ts
- tailwind.config.js
- postcss.config.js
- tsconfig.json
- index.html
- src/main.tsx
- src/App.tsx
- src/index.css
- src/components/ (at least 3 components)
- src/pages/ (at least 2 pages)
- src/hooks/ (custom hooks)
- src/utils/ (utility functions)
- src/types/ (TypeScript types)

ROOT:
- README.md (comprehensive documentation)
- docker-compose.yml
- .gitignore

Return in this EXACT format:
// FILE: filepath
// CONTENT:
file content here
// END FILE

IMPORTANT: Generate COMPLETE, FUNCTIONAL code for each file. No placeholders or comments like "// Add more code here". Every file must be production-ready.
[/INST]</s>"""

        llm = models[model_type]
        structure = llm(structure_prompt, max_tokens=8192, temperature=0.1, stop=["</s>"])
        if not structure or "choices" not in structure:
            raise ValueError("Failed to generate structure")

        files = parse_project_files(structure["choices"][0]["text"])
        if not files:
            raise ValueError("No files generated")

        for file_info in files:
            file_path = project_dir / file_info["path"]
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(file_info["content"])

        project_info = {
            "id": project_id,
            "name": f"Project {project_id[:8]}",
            "description": prompt[:100] + "..." if len(prompt) > 100 else prompt,
            "created_at": datetime.utcnow().isoformat(),
            "files": [f["path"] for f in files],
            "user_id": user_id or ""
        }

        with open(project_dir / "project_info.json", "w") as f:
            json.dump(project_info, f)

        return {"project": {"id": project_id, "files": files}}

    except Exception as e:
        logger.error(f"Error generating code: {str(e)}")
        raise

def parse_project_files(generated_text: str) -> List[Dict]:
    """Parse AI-generated text into file objects"""
    files = []
    current_file = None
    current_content = []
    in_content = False

    lines = generated_text.splitlines()
    for line in lines:
        line_stripped = line.strip()

        if line_stripped.startswith("// FILE:"):
            # Save previous file if exists
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_content = []

            # Start new file
            filepath = line_stripped.replace("// FILE:", "").strip()
            current_file = {"path": filepath, "content": ""}
            in_content = False

        elif line_stripped.startswith("// CONTENT:"):
            current_content = []
            in_content = True

        elif line_stripped.startswith("// END FILE"):
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_file = None
                current_content = []
            in_content = False

        elif in_content and current_file is not None:
            # Preserve original line formatting (don't strip)
            current_content.append(line)

        # Also handle code blocks format ```filepath
        elif line_stripped.startswith("```") and not line_stripped == "```":
            # Save previous file if exists
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_content = []

            # Start new file from code block
            filepath = line_stripped.replace("```", "").strip()
            if filepath:  # Only if there's a filepath
                current_file = {"path": filepath, "content": ""}
                in_content = True

        elif line_stripped == "```" and current_file:
            # End of code block
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_file = None
                current_content = []
            in_content = False

        elif in_content and current_file is not None and not line_stripped.startswith("//"):
            # Add content line (preserve formatting)
            current_content.append(line)

    # Handle last file
    if current_file and current_content:
        current_file["content"] = "\n".join(current_content)
        files.append(current_file)

    return files

def generate_single_file_code(prompt: str, model_type: str = "vikki") -> str:
    """Generate a single file's code"""
    try:
        # Validate model type
        if model_type not in models:
            if model_type == "vikki_advanced":
                raise ValueError("VIKKI AI Advanced model is not available. Please use VIKKI AI instead.")
            else:
                raise ValueError(f"Invalid model type: {model_type}")

        file_prompt = f"""<s>[INST]
Generate code for: {prompt}
Return ONLY the code, no explanations.
[/INST]</s>"""

        llm = models[model_type]
        output = llm(file_prompt, max_tokens=2048, temperature=0.1, stop=["</s>"])
        return output["choices"][0]["text"].strip()
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        raise
