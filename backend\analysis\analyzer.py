import os
import re
from typing import List, Dict, Any
import ast
import javalang
import esprima
from pathlib import Path

class ProjectAnalyzer:
    def __init__(self):
        self.supported_extensions = {
            '.py': self._analyze_python,
            '.java': self._analyze_java,
            '.js': self._analyze_javascript,
            '.jsx': self._analyze_javascript,
            '.ts': self._analyze_javascript,
            '.tsx': self._analyze_javascript,
        }

    def analyze_project(self, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze a project and return analysis results."""
        total_lines = 0
        languages = set()
        quality_issues = []
        performance_issues = []
        security_issues = []

        for file in files:
            file_path = file['path']
            content = file['content']
            extension = os.path.splitext(file_path)[1].lower()

            if extension in self.supported_extensions:
                languages.add(extension[1:])  # Remove the dot
                total_lines += len(content.splitlines())

                # Analyze file based on its type
                analyzer = self.supported_extensions[extension]
                file_issues = analyzer(content, file_path)

                quality_issues.extend(file_issues.get('quality', []))
                performance_issues.extend(file_issues.get('performance', []))
                security_issues.extend(file_issues.get('security', []))

        return {
            'totalFiles': len(files),
            'totalLines': total_lines,
            'languages': list(languages),
            'qualityIssues': quality_issues,
            'performanceIssues': performance_issues,
            'securityIssues': security_issues
        }

    def _analyze_python(self, content: str, file_path: str) -> Dict[str, List[Dict[str, Any]]]:
        """Analyze Python code."""
        issues = {
            'quality': [],
            'performance': [],
            'security': []
        }

        try:
            tree = ast.parse(content)
            
            # Check for long functions
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if len(node.body) > 20:
                        issues['quality'].append({
                            'title': 'Long Function',
                            'description': f'Function {node.name} is too long ({len(node.body)} lines)',
                            'severity': 'medium',
                            'suggestion': 'Consider breaking down the function into smaller, more focused functions.'
                        })

            # Check for security issues
            if 'eval(' in content or 'exec(' in content:
                issues['security'].append({
                    'title': 'Use of eval/exec',
                    'description': 'The code uses eval() or exec() which can be dangerous',
                    'severity': 'high',
                    'recommendation': 'Avoid using eval() or exec(). Consider using safer alternatives.'
                })

            # Check for performance issues
            if 'for' in content and 'range(' in content and 'len(' in content:
                issues['performance'].append({
                    'title': 'Inefficient Loop',
                    'description': 'Using range(len()) in a for loop is less efficient',
                    'severity': 'low',
                    'optimization': 'Consider using enumerate() instead of range(len()).'
                })

        except SyntaxError as e:
            issues['quality'].append({
                'title': 'Syntax Error',
                'description': f'Syntax error in {file_path}: {str(e)}',
                'severity': 'high'
            })

        return issues

    def _analyze_java(self, content: str, file_path: str) -> Dict[str, List[Dict[str, Any]]]:
        """Analyze Java code."""
        issues = {
            'quality': [],
            'performance': [],
            'security': []
        }

        try:
            tree = javalang.parse.parse(content)
            
            # Check for long methods
            for path, node in tree.filter(javalang.tree.MethodDeclaration):
                if len(node.body.statements) > 20:
                    issues['quality'].append({
                        'title': 'Long Method',
                        'description': f'Method {node.name} is too long ({len(node.body.statements)} statements)',
                        'severity': 'medium',
                        'suggestion': 'Consider breaking down the method into smaller, more focused methods.'
                    })

            # Check for security issues
            if 'Runtime.getRuntime().exec(' in content:
                issues['security'].append({
                    'title': 'Command Execution',
                    'description': 'The code uses Runtime.exec() which can be dangerous',
                    'severity': 'high',
                    'recommendation': 'Avoid using Runtime.exec(). Consider using safer alternatives.'
                })

            # Check for performance issues
            if 'new ArrayList<>()' in content and 'for' in content:
                issues['performance'].append({
                    'title': 'Inefficient List Creation',
                    'description': 'Creating ArrayList without initial capacity in a loop',
                    'severity': 'low',
                    'optimization': 'Consider specifying initial capacity for ArrayList to avoid resizing.'
                })

        except javalang.parser.JavaSyntaxError as e:
            issues['quality'].append({
                'title': 'Syntax Error',
                'description': f'Syntax error in {file_path}: {str(e)}',
                'severity': 'high'
            })

        return issues

    def _analyze_javascript(self, content: str, file_path: str) -> Dict[str, List[Dict[str, Any]]]:
        """Analyze JavaScript/TypeScript code."""
        issues = {
            'quality': [],
            'performance': [],
            'security': []
        }

        try:
            tree = esprima.parseScript(content, {'loc': True})
            
            # Check for long functions
            for node in tree.body:
                if node.type == 'FunctionDeclaration' and node.body.body.length > 20:
                    issues['quality'].append({
                        'title': 'Long Function',
                        'description': f'Function {node.id.name} is too long ({len(node.body.body)} lines)',
                        'severity': 'medium',
                        'suggestion': 'Consider breaking down the function into smaller, more focused functions.'
                    })

            # Check for security issues
            if 'eval(' in content:
                issues['security'].append({
                    'title': 'Use of eval',
                    'description': 'The code uses eval() which can be dangerous',
                    'severity': 'high',
                    'recommendation': 'Avoid using eval(). Consider using safer alternatives.'
                })

            # Check for performance issues
            if 'for' in content and 'length' in content:
                issues['performance'].append({
                    'title': 'Inefficient Loop',
                    'description': 'Using array.length in a for loop can be optimized',
                    'severity': 'low',
                    'optimization': 'Consider caching array.length before the loop or using for...of loop.'
                })

        except esprima.error_handler.Error as e:
            issues['quality'].append({
                'title': 'Syntax Error',
                'description': f'Syntax error in {file_path}: {str(e)}',
                'severity': 'high'
            })

        return issues 