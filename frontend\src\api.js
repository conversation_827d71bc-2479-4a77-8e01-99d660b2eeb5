import axios from './config/axios'

const API_BASE = "/api";

// Authentication
export const login = (username, password) => {
  const formData = new FormData()
  formData.append('username', username)
  formData.append('password', password)
  formData.append('grant_type', 'password')
  
  return axios.post('/api/token', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).then(res => res.data)
}

export const register = (userData) =>
  axios.post('/api/register', userData).then(res => res.data)

export const getCurrentUser = () =>
  axios.get('/api/users/me').then(res => res.data)

// User stats and projects
export const getUserStats = () =>
  axios.get('/users/stats').then(res => res.data)

export const getUserProjects = () =>
  axios.get('/users/projects').then(res => res.data)

// Code generation
export const generateCode = async (prompt) => {
    try {
        const response = await axios.post('/api/generate', { prompt });
        return response.data;
    } catch (error) {
        console.error('API Error:', error);

        // Handle token exhaustion error
        if (error.response?.status === 402) {
            const tokenError = new Error('Insufficient tokens');
            tokenError.tokenInfo = error.response.data;
            throw tokenError;
        }

        throw error;
    }
};

// Project management
export const createProject = (projectData) =>
  axios.post('/projects', projectData).then(res => res.data)

export const getProject = (id) =>
  axios.get(`/projects/${id}`).then(res => res.data)

export const updateProject = (id, projectData) =>
  axios.put(`/projects/${id}`, projectData).then(res => res.data)

export const deleteProject = (id) =>
  axios.delete(`/projects/${id}`).then(res => res.data)

// API documentation
export const getApiDocs = () =>
  axios.get('/api-docs').then(res => res.data)

// Project analysis
export const analyzeProject = (projectId) =>
  axios.post(`/projects/${projectId}/analyze`).then(res => res.data)

// API Generation
export const getProjects = () =>
  axios.get('/api/projects').then(res => res.data)

export const downloadProject = (id) =>
  axios.get(`/api/projects/${id}/download`, { responseType: 'blob' })

// Forum
export const getForumTopics = (params) =>
  axios.get('/api/forum/topics', { params }).then(res => res.data)

export const getForumTopic = (id) =>
  axios.get(`/api/forum/topics/${id}`).then(res => res.data)

export const createForumTopic = (data) =>
  axios.post('/api/forum/topics', data).then(res => res.data)

export const addForumReply = (topicId, data) =>
  axios.post(`/api/forum/topics/${topicId}/replies`, data).then(res => res.data)

export const likeTopic = (topicId) =>
  axios.post(`/api/forum/topics/${topicId}/like`).then(res => res.data)

// User Profile
export const updateUserProfile = (data) =>
  axios.put('/api/users/profile', data).then(res => res.data)

// Contact
export const submitContactForm = (formData) =>
  axios.post('/api/contact', formData).then(res => res.data)

export const chat = async (message, model = 'vikki', projectId = null, projectPath = null) => {
  try {
    const requestData = {
      message,
      model
    };

    // Add project context if provided
    if (projectId) {
      requestData.project_id = projectId;
    }
    if (projectPath) {
      requestData.project_path = projectPath;
    }

    const response = await axios.post('/api/chat', requestData);

    if (!response.data) {
      throw new Error('Failed to get chat response');
    }

    return response.data.response;
  } catch (error) {
    console.error('Chat error:', error);

    // Handle token exhaustion error
    if (error.response?.status === 402) {
      const tokenError = new Error('Insufficient tokens');
      tokenError.tokenInfo = error.response.data;
      throw tokenError;
    }

    throw error;
  }
};

// API Key management
export const getUserApiKey = () =>
  axios.get('/api/users/api-key').then(res => res.data)

export const regenerateUserApiKey = () =>
  axios.post('/api/users/api-key/regenerate').then(res => res.data)