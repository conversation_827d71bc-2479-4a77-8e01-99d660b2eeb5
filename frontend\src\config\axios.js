import axios from 'axios'

const instance = axios.create({
  baseURL: '',
  timeout: 900000, // 15 minutes timeout
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
instance.interceptors.request.use(
  (config) => {
    // Check both localStorage and sessionStorage for token
    const token = localStorage.getItem('token') || sessionStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
instance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout - the server is taking too long to respond')
      return Promise.reject(new Error('The request is taking longer than expected. Please try again.'))
    }

    if (error.response?.status === 401) {
      // Clear tokens from both storage locations
      localStorage.removeItem('token')
      localStorage.removeItem('remember_me')
      sessionStorage.removeItem('token')
      window.location.href = '/login'
    }

    return Promise.reject(error)
  }
)

export default instance 