import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { FiDownload, FiEye, FiCode, FiClock, FiCheck, FiFile, FiFolder } from 'react-icons/fi'
import toast from 'react-hot-toast'
import axios from '../config/axios'


const Dashboard = () => {
  const [projects, setProjects] = useState([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalProjects: 0,
    apiCalls: 0,
    successRate: 100,
    totalViews: 0
  })
  const [selectedProject, setSelectedProject] = useState(null)
  const [selectedFile, setSelectedFile] = useState(null)
  const navigate = useNavigate()

  useEffect(() => {
    fetchProjects()
    fetchStats()
  }, [])

  const fetchProjects = async () => {
    try {
      const response = await axios.get('/api/users/projects')
      console.log('Projects response:', response.data)

      if (Array.isArray(response.data)) {
        setProjects(response.data)
      } else {
        console.warn('Projects response is not an array:', response.data)
        setProjects([])
        // Don't show error for empty projects - it's normal for new users
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
      if (error.response?.status === 401) {
        toast.error('Please log in to view your projects')
      } else if (error.response?.status === 500) {
        console.error('Server error fetching projects')
        // Don't show error toast for server errors - just log them
      } else {
        toast.error('Failed to load projects')
      }
      setProjects([])
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/users/stats')
      setStats(response.data)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleDownload = async (projectId) => {
    try {
      const response = await axios.get(`/api/projects/${projectId}/download`, {
        responseType: 'blob'
      })

      const url = window.URL.createObjectURL(new Blob([response.data]))
      const a = document.createElement('a')
      a.href = url
      a.download = `project_${projectId}.zip`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      toast.success('Project downloaded successfully!')
    } catch (error) {
      console.error('Download error:', error)
      toast.error('Failed to download project')
    }
  }

  const handlePreview = async (projectId) => {
    try {
      // First get the project files for the file tree
      const projectResponse = await axios.get(`/api/projects/${projectId}/preview`)
      setSelectedProject(projectResponse.data)
      setSelectedFile(null)

      // Then try to create a live preview
      try {
        const previewResponse = await axios.post(`/api/preview/${projectId}`)
        if (previewResponse.data.preview_url) {
          // Get the auth token
          const token = localStorage.getItem('token') || sessionStorage.getItem('token')

          // Open the preview in a new tab/window with token as query parameter
          const previewUrl = `${axios.defaults.baseURL}${previewResponse.data.preview_url}?token=${token}`
          window.open(previewUrl, '_blank', 'width=1200,height=800')
          toast.success('Preview opened in new window')
        }
      } catch (previewError) {
        console.log('Live preview not available, showing file structure')
      }
    } catch (error) {
      console.error('Preview error:', error)
      toast.error('Failed to load project preview')
    }
  }

  const handleGenerateNew = () => {
    navigate('/generate')
  }

  const renderFileTree = (files) => {
    const fileTree = {}
    
    // Build file tree structure
    files.forEach(file => {
      const parts = file.split('/')
      let current = fileTree
      
      parts.forEach((part, index) => {
        if (index === parts.length - 1) {
          current[part] = { type: 'file', path: file }
        } else {
          current[part] = current[part] || { type: 'folder', children: {} }
          current = current[part].children
        }
      })
    })
    
    const renderNode = (node, name, path = '') => {
      if (node.type === 'file') {
        return (
          <div
            key={path + name}
            className="flex items-center px-4 py-2 hover:bg-gray-700 cursor-pointer"
            onClick={() => setSelectedFile({ name, content: selectedProject.file_contents[node.path] })}
          >
            <FiFile className="mr-2 text-blue-400" />
            <span className="text-gray-300">{name}</span>
          </div>
        )
      }
      
      return (
        <div key={path + name} className="ml-4">
          <div className="flex items-center px-4 py-2">
            <FiFolder className="mr-2 text-yellow-400" />
            <span className="text-gray-300">{name}</span>
          </div>
          {Object.entries(node.children).map(([childName, childNode]) =>
            renderNode(childNode, childName, path + name + '/')
          )}
        </div>
      )
    }
    
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        {Object.entries(fileTree).map(([name, node]) => renderNode(node, name))}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#181a20] via-[#232526] to-[#23243a] text-white font-sans pt-20" style={{ fontFamily: 'Inter, Space Grotesk, sans-serif' }}>
      <div className="max-w-7xl mx-auto px-6 py-12">
        <h1 className="text-4xl font-extrabold mb-10 bg-gradient-to-r from-cyan-400 to-blue-600 text-transparent bg-clip-text drop-shadow-glow tracking-wider">Dashboard</h1>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
          <div className="lg:col-span-2 space-y-10">
            {/* Stats Section */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-10">
              <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-2xl p-8 shadow-2xl backdrop-blur-xl flex flex-col items-center justify-center">
                <div className="flex items-center gap-4">
                  <FiCode className="text-cyan-400 text-4xl animate-pulse drop-shadow-glow" />
                  <div>
                    <p className="text-cyan-200/80 text-lg font-semibold">Total Projects</p>
                    <p className="text-3xl font-extrabold text-white tracking-wider">{stats.totalProjects}</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-2xl p-8 shadow-2xl backdrop-blur-xl flex flex-col items-center justify-center">
                <div className="flex items-center gap-4">
                  <FiClock className="text-blue-400 text-4xl animate-spin-slow drop-shadow-glow" />
                  <div>
                    <p className="text-cyan-200/80 text-lg font-semibold">API Calls</p>
                    <p className="text-3xl font-extrabold text-white tracking-wider">{stats.apiCalls}</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-2xl p-8 shadow-2xl backdrop-blur-xl flex flex-col items-center justify-center">
                <div className="flex items-center gap-4">
                  <FiCheck className="text-green-400 text-4xl animate-bounce drop-shadow-glow" />
                  <div>
                    <p className="text-cyan-200/80 text-lg font-semibold">Success Rate</p>
                    <p className="text-3xl font-extrabold text-white tracking-wider">{stats.successRate}%</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-2xl p-8 shadow-2xl backdrop-blur-xl flex flex-col items-center justify-center">
                <div className="flex items-center gap-4">
                  <FiEye className="text-purple-400 text-4xl animate-pulse drop-shadow-glow" />
                  <div>
                    <p className="text-cyan-200/80 text-lg font-semibold">Total Views</p>
                    <p className="text-3xl font-extrabold text-white tracking-wider">{stats.totalViews}</p>
                  </div>
                </div>
              </div>
            </div>
            {/* Projects Section */}
            <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-3xl shadow-2xl p-8 backdrop-blur-xl">
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-2xl font-bold text-cyan-100 tracking-wide">Your Projects</h2>
                <button
                  onClick={handleGenerateNew}
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-bold rounded-xl shadow-lg hover:from-cyan-400 hover:to-blue-500 transition-all focus:outline-none focus:ring-2 focus:ring-cyan-400"
                >
                  <FiCode className="mr-2 animate-pulse" />
                  Generate New Project
                </button>
              </div>
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <span className="text-cyan-200 animate-pulse text-lg">Loading projects...</span>
                </div>
              ) : projects.length === 0 ? (
                <div className="text-cyan-200/60 text-lg text-center py-8">No projects found. Start by generating a new project!</div>
              ) : (
                <div className="space-y-6">
                  {projects.map((project) => (
                    <div key={project.id} className="bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 rounded-xl p-6 flex flex-col md:flex-row md:items-center md:justify-between shadow-lg hover:shadow-2xl transition-all border border-cyan-900/20">
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-cyan-100 mb-2">{project.name || `Project ${project.id.slice(0, 8)}`}</h3>
                        <p className="text-cyan-200/80 mb-2">{project.description}</p>
                        <div className="flex flex-wrap gap-2 text-xs text-cyan-400/80">
                          {project.files && project.files.map((file, idx) => (
                            <span key={idx} className="bg-cyan-900/30 px-2 py-1 rounded-full font-mono">{file}</span>
                          ))}
                        </div>
                      </div>
                      <div className="flex gap-4 mt-4 md:mt-0">
                        <button
                          onClick={() => handlePreview(project.id)}
                          className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-cyan-700 to-blue-700 text-white rounded-lg shadow hover:from-cyan-600 hover:to-blue-600 transition-all"
                        >
                          <FiEye className="mr-2" /> Live Preview
                        </button>
                        <button
                          onClick={() => handleDownload(project.id)}
                          className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-700 to-purple-700 text-white rounded-lg shadow hover:from-blue-600 hover:to-purple-600 transition-all"
                        >
                          <FiDownload className="mr-2" /> Download
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
          {/* Right Sidebar */}
          <div className="space-y-6">
            {/* Project Preview Section */}
            <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-3xl shadow-2xl p-8 backdrop-blur-xl flex flex-col gap-6">
              <h2 className="text-xl font-bold text-cyan-100 mb-4 tracking-wide">Project Preview</h2>
            {!selectedProject ? (
              <div className="text-cyan-200/60 text-lg text-center py-8">Select a project to preview its files and code.</div>
            ) : (
              <>
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-cyan-200 mb-2">Files</h3>
                  {selectedProject.files && selectedProject.files.length > 0 ? (
                    <div className="bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 rounded-xl p-4">
                      {renderFileTree(selectedProject.files)}
                    </div>
                  ) : (
                    <div className="text-cyan-200/60">No files found for this project.</div>
                  )}
                </div>
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-cyan-200 mb-2">Code Preview</h3>
                  {selectedFile ? (
                    <pre className="bg-[#181a20] rounded-xl p-4 text-cyan-100 overflow-x-auto shadow-inner border border-cyan-900/20">
                      {selectedFile.content}
                    </pre>
                  ) : (
                    <div className="text-cyan-200/60">Select a file to view its code.</div>
                  )}
                </div>
              </>
            )}
            </div>
          </div>
        </div>
      </div>
      <style>{`
        .drop-shadow-glow {
          filter: drop-shadow(0 0 6px #00fff7cc);
        }
        .animate-spin-slow {
          animation: spin 2.5s linear infinite;
        }
      `}</style>
    </div>
  )
}

export default Dashboard 