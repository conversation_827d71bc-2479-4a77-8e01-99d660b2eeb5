from llama_cpp import <PERSON>lama
import time
from typing import Dict, List, Generator
import os
from pathlib import Path
import json
from datetime import datetime
import shutil
import uuid
import logging
import re
import asyncio

logger = logging.getLogger(__name__)

# Model paths mapping
MODEL_PATHS = {
    "vikki": "models/mistral-7b-instruct-v0.2.Q5_K_M.gguf",
    "vikki_advanced": "models/Codestral-22B-v0.1-Q4_K_M.gguf"
}

# Initialize models
models = {}

# Try to load VIKKI AI (Mistral) model
try:
    models["vikki"] = Llama(
        model_path=MODEL_PATHS["vikki"],
        n_ctx=1500,
        n_threads=8
    )
    logger.info("Successfully loaded VIKKI AI model")
except Exception as e:
    logger.error(f"Failed to load VIKKI AI model: {str(e)}")
    raise RuntimeError("Failed to load VIKKI AI model - this is required")

# Try to load VIKKI AI Advanced (Codestral) model
try:
    models["vikki_advanced"] = <PERSON>lama(
        model_path=MODEL_PATHS["vikki_advanced"],
        n_ctx=1500,
        n_threads=8,
        n_gpu_layers=0  # Force CPU only to reduce memory usage
    )
    logger.info("Successfully loaded VIKKI AI Advanced model")
except Exception as e:
    logger.warning(f"Failed to load VIKKI AI Advanced model: {str(e)}")
    logger.warning("VIKKI AI Advanced features will be disabled")
    # Don't raise error, just log warning

def generate_code_streaming(prompt: str, model_type: str = "vikki", user_id: str = None) -> Generator[Dict, None, None]:
    """Generate code with streaming updates and live progress"""
    try:
        # Map 'basic' and 'advanced' to the correct model keys
        if model_type == "basic":
            model_type = "vikki"
        elif model_type == "advanced":
            model_type = "vikki_advanced"
        # Validate model type
        if model_type not in models:
            if model_type == "vikki_advanced":
                yield {
                    "type": "error",
                    "data": {
                        "error": "VIKKI AI Advanced model is not available. Please use VIKKI AI instead."
                    }
                }
                return
            else:
                yield {
                    "type": "error",
                    "data": {
                        "error": f"Invalid model type: {model_type}"
                    }
                }
                return

        project_id = str(uuid.uuid4())
        project_dir = Path("generated_projects") / project_id
        project_dir.mkdir(parents=True, exist_ok=True)

        structure_prompt = f"""<s>[INST]
Generate a complete, production-ready full-stack project for: {prompt}

Requirements:
- Frontend: React (Vite, Tailwind CSS)
- Backend: FastAPI (Python)
- Include a Dockerfile for the backend
- Include a README.md
- All config files (env, etc.) and best practices

Return the project structure in this exact format:
// FOLDER: folder_name
// FILE: file_path
// CONTENT:
file content here
// END FILE

Make sure to:
1. Create all necessary folders and files for a real-world deployment
2. Include complete file contents
3. Use proper file extensions
4. Follow best practices for the technology stack
5. Include configuration files (Dockerfile, .env, etc.)
6. Add proper documentation

Return ONLY the folder and file structure, no explanations.
[/INST]</s>"""

        yield {
            "type": "status",
            "data": {
                "status": "generating",
                "message": "Generating project structure..."
            }
        }

        llm = models[model_type]
        structure = llm(structure_prompt, max_tokens=4096, temperature=0.1, stop=["</s>"])
        print("Model output:", structure)  # Debug print

        if not structure or "choices" not in structure:
            raise ValueError("Failed to generate project structure (no choices in model output)")

        if not structure["choices"] or "text" not in structure["choices"][0]:
            raise ValueError("Model output missing 'text' in choices")

        files = parse_project_files(structure["choices"][0]["text"])
        if not isinstance(files, list) or not files:
            raise ValueError("No files were generated or files list is invalid")

        total_files = len(files)
        for index, file_info in enumerate(files, 1):
            file_path = project_dir / file_info["path"]
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(file_info["content"])

            yield {
                "type": "file",
                "data": {
                    "file": file_info,
                    "progress": {
                        "current": index,
                        "total": total_files,
                        "percentage": (index / total_files) * 100
                    }
                }
            }

        project_info = {
            "id": project_id,
            "name": f"Project {project_id[:8]}",
            "description": prompt[:100] + "..." if len(prompt) > 100 else prompt,
            "created_at": datetime.utcnow().isoformat(),
            "files": [f["path"] for f in files],
            "user_id": user_id or ""
        }

        with open(project_dir / "project_info.json", "w") as f:
            json.dump(project_info, f)

        yield {
            "type": "complete",
            "data": {
                "project": {
                    "id": project_id,
                    "files": files
                }
            }
        }

    except Exception as e:
        logger.error(f"Error generating code: {str(e)}")
        yield {
            "type": "error",
            "data": {
                "error": str(e)
            }
        }

def generate_code(prompt: str, model_type: str = "vikki", user_id: str = None) -> Dict:
    """Generate full code with selected model"""
    try:
        # Map 'basic' and 'advanced' to the correct model keys
        if model_type == "basic":
            model_type = "vikki"
        elif model_type == "advanced":
            model_type = "vikki_advanced"
        # Validate model type
        if model_type not in models:
            if model_type == "vikki_advanced":
                raise ValueError("VIKKI AI Advanced model is not available. Please use VIKKI AI instead.")
            else:
                raise ValueError(f"Invalid model type: {model_type}")

        project_id = str(uuid.uuid4())
        project_dir = Path("generated_projects") / project_id
        project_dir.mkdir(parents=True, exist_ok=True)

        structure_prompt = f"""<s>[INST]
Generate a complete, production-ready full-stack project for: {prompt}

Requirements:
- Frontend: React (Vite, Tailwind CSS)
- Backend: FastAPI (Python)
- Include a Dockerfile for the backend
- Include a README.md
- All config files (env, etc.) and best practices

Return the project structure in this exact format:
// FOLDER: folder_name
// FILE: file_path
// CONTENT:
file content here
// END FILE

Make sure to:
1. Create all necessary folders and files for a real-world deployment
2. Include complete file contents
3. Use proper file extensions
4. Follow best practices for the technology stack
5. Include configuration files (Dockerfile, .env, etc.)
6. Add proper documentation

Return ONLY the folder and file structure, no explanations.
[/INST]</s>"""

        llm = models[model_type]
        structure = llm(structure_prompt, max_tokens=4096, temperature=0.1, stop=["</s>"])
        if not structure or "choices" not in structure:
            raise ValueError("Failed to generate structure")

        files = parse_project_files(structure["choices"][0]["text"])
        if not files:
            raise ValueError("No files generated")

        for file_info in files:
            file_path = project_dir / file_info["path"]
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(file_info["content"])

        project_info = {
            "id": project_id,
            "name": f"Project {project_id[:8]}",
            "description": prompt[:100] + "..." if len(prompt) > 100 else prompt,
            "created_at": datetime.utcnow().isoformat(),
            "files": [f["path"] for f in files],
            "user_id": user_id or ""
        }

        with open(project_dir / "project_info.json", "w") as f:
            json.dump(project_info, f)

        return {"project": {"id": project_id, "files": files}}

    except Exception as e:
        logger.error(f"Error generating code: {str(e)}")
        raise

def parse_project_files(generated_text: str) -> List[Dict]:
    """Parse AI-generated text into file objects"""
    files = []
    current_file = None
    current_folder = None
    current_content = []

    lines = generated_text.splitlines()
    for line in lines:
        line = line.strip()
        if not line:
            continue
        if line.startswith("// FOLDER:"):
            current_folder = line.replace("// FOLDER:", "").strip()
        elif line.startswith("// FILE:"):
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_content = []

            filepath = line.replace("// FILE:", "").strip()
            if current_folder and not filepath.startswith(current_folder):
                filepath = f"{current_folder}/{filepath}"
            current_file = {"path": filepath, "content": ""}
        elif line.startswith("// CONTENT:"):
            current_content = []
        elif line.startswith("// END FILE"):
            if current_file and current_content:
                current_file["content"] = "\n".join(current_content)
                files.append(current_file)
                current_file = None
                current_content = []
        elif current_file is not None:
            current_content.append(line)

    if current_file and current_content:
        current_file["content"] = "\n".join(current_content)
        files.append(current_file)

    return files

def generate_single_file_code(prompt: str, model_type: str = "vikki") -> str:
    """Generate a single file's code"""
    try:
        # Validate model type
        if model_type not in models:
            if model_type == "vikki_advanced":
                raise ValueError("VIKKI AI Advanced model is not available. Please use VIKKI AI instead.")
            else:
                raise ValueError(f"Invalid model type: {model_type}")

        file_prompt = f"""<s>[INST]
Generate code for: {prompt}
Return ONLY the code, no explanations.
[/INST]</s>"""

        llm = models[model_type]
        output = llm(file_prompt, max_tokens=2048, temperature=0.1, stop=["</s>"])
        return output["choices"][0]["text"].strip()
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        raise
