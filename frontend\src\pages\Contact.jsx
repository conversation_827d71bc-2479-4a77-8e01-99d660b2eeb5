import { useState } from 'react'
import { FiMail, FiPhone, FiMapPin, FiSend, FiTwitter, FiGithub, FiLinkedin } from 'react-icons/fi'
import toast from 'react-hot-toast'
import HCaptcha from '@hcaptcha/react-hcaptcha'

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  })
  const [captchaToken, setCaptchaToken] = useState('')

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!captchaToken) {
      toast.error('Please complete the captcha verification')
      return
    }

    try {
      // Replace with your actual API endpoint
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          captcha_token: captchaToken
        }),
      })

      if (response.ok) {
        toast.success('Message sent successfully!')
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: '',
        })
        setCaptchaToken('')
      } else {
        toast.error('Failed to send message. Please try again.')
      }
    } catch (error) {
      toast.error('An error occurred. Please try again later.')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#181a20] via-[#232526] to-[#23243a] text-white font-sans py-12" style={{ fontFamily: 'Inter, Space Grotesk, sans-serif' }}>
      <div className="max-w-7xl mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold bg-gradient-to-r from-cyan-400 to-blue-600 text-transparent bg-clip-text drop-shadow-glow tracking-wider mb-4">Contact Us</h1>
          <p className="mt-4 text-lg text-cyan-200/80 font-medium">Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
        </div>
        <div className="mt-12 grid grid-cols-1 gap-10 lg:grid-cols-2">
          {/* Contact Information */}
          <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-3xl shadow-2xl p-10 flex flex-col gap-8 backdrop-blur-xl">
            <h2 className="text-2xl font-bold text-cyan-100 mb-6">Get in Touch</h2>
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <FiMail className="h-7 w-7 text-cyan-400 animate-pulse drop-shadow-glow" />
                <div>
                  <p className="text-base font-semibold text-cyan-100">Email</p>
                  <p className="text-cyan-200/80 font-medium"><EMAIL></p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <FiPhone className="h-7 w-7 text-cyan-400 animate-pulse drop-shadow-glow" />
                <div>
                  <p className="text-base font-semibold text-cyan-100">Phone</p>
                  <p className="text-cyan-200/80 font-medium">+****************</p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <FiMapPin className="h-7 w-7 text-cyan-400 animate-pulse drop-shadow-glow" />
                <div>
                  <p className="text-base font-semibold text-cyan-100">Office</p>
                  <p className="text-cyan-200/80 font-medium">123 Developer Street<br />San Francisco, CA 94107</p>
                </div>
              </div>
            </div>
            <div className="mt-8">
              <h3 className="text-lg font-semibold text-cyan-100 mb-4">Follow Us</h3>
              <div className="flex space-x-4">
                <a href="#" className="text-cyan-400 hover:text-cyan-200 transition-colors animate-pulse"><FiTwitter className="w-6 h-6" /></a>
                <a href="#" className="text-cyan-400 hover:text-cyan-200 transition-colors animate-pulse"><FiGithub className="w-6 h-6" /></a>
                <a href="#" className="text-cyan-400 hover:text-cyan-200 transition-colors animate-pulse"><FiLinkedin className="w-6 h-6" /></a>
              </div>
            </div>
          </div>
          {/* Contact Form */}
          <div className="bg-gradient-to-br from-[#23243a]/80 to-[#181a20]/90 rounded-3xl shadow-2xl p-10 backdrop-blur-xl">
            <form onSubmit={handleSubmit} className="space-y-8">
              <div>
                <label htmlFor="name" className="block text-base font-semibold text-cyan-100 mb-2">Name</label>
                <input type="text" id="name" name="name" value={formData.name} onChange={handleChange} required className="mt-1 block w-full rounded-xl border-cyan-900/20 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 text-cyan-100 shadow-inner px-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-medium" />
              </div>
              <div>
                <label htmlFor="email" className="block text-base font-semibold text-cyan-100 mb-2">Email</label>
                <input type="email" id="email" name="email" value={formData.email} onChange={handleChange} required className="mt-1 block w-full rounded-xl border-cyan-900/20 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 text-cyan-100 shadow-inner px-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-medium" />
              </div>
              <div>
                <label htmlFor="subject" className="block text-base font-semibold text-cyan-100 mb-2">Subject</label>
                <input type="text" id="subject" name="subject" value={formData.subject} onChange={handleChange} required className="mt-1 block w-full rounded-xl border-cyan-900/20 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 text-cyan-100 shadow-inner px-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-medium" />
              </div>
              <div>
                <label htmlFor="message" className="block text-base font-semibold text-cyan-100 mb-2">Message</label>
                <textarea id="message" name="message" value={formData.message} onChange={handleChange} required className="mt-1 block w-full rounded-xl border-cyan-900/20 bg-gradient-to-r from-[#23243a]/60 to-[#181a20]/60 text-cyan-100 shadow-inner px-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-medium min-h-[120px]" />
              </div>

              {/* hCaptcha */}
              <div className="flex justify-center">
                <HCaptcha
                  sitekey="e1f67dd6-6e08-4a28-afc0-cb087ebe7b8a"
                  onVerify={(token) => setCaptchaToken(token)}
                  onExpire={() => setCaptchaToken('')}
                  onError={() => setCaptchaToken('')}
                  theme="dark"
                />
              </div>

              <button
                type="submit"
                disabled={!captchaToken}
                className="w-full bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold py-3 px-6 rounded-xl shadow-lg transition-all text-lg focus:outline-none focus:ring-2 focus:ring-cyan-400 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Send Message <FiSend className="inline ml-2 animate-pulse" />
              </button>
            </form>
          </div>
        </div>
        <style>{`.drop-shadow-glow { filter: drop-shadow(0 0 6px #00fff7cc); }`}</style>
      </div>
    </div>
  )
} 