# -*- coding: utf-8 -*-
# Copyright JS Foundation and other contributors, https://js.foundation/
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
#   * Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#   * Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
# THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

from __future__ import unicode_literals


class Syntax:
    AssignmentExpression = "AssignmentExpression"
    AssignmentPattern = "AssignmentPattern"
    ArrayExpression = "ArrayExpression"
    ArrayPattern = "ArrayPattern"
    ArrowFunctionExpression = "ArrowFunctionExpression"
    AwaitExpression = "AwaitExpression"
    BlockStatement = "BlockStatement"
    BinaryExpression = "BinaryExpression"
    BreakStatement = "BreakStatement"
    CallExpression = "CallExpression"
    CatchClause = "CatchClause"
    ClassBody = "ClassBody"
    ClassDeclaration = "ClassDeclaration"
    ClassExpression = "ClassExpression"
    ConditionalExpression = "ConditionalExpression"
    ContinueStatement = "ContinueStatement"
    DoWhileStatement = "DoWhileStatement"
    DebuggerStatement = "DebuggerStatement"
    EmptyStatement = "EmptyStatement"
    ExportAllDeclaration = "ExportAllDeclaration"
    ExportDefaultDeclaration = "ExportDefaultDeclaration"
    ExportNamedDeclaration = "ExportNamedDeclaration"
    ExportSpecifier = "ExportSpecifier"
    ExportDefaultSpecifier = "ExportDefaultSpecifier"
    ExpressionStatement = "ExpressionStatement"
    ForStatement = "ForStatement"
    ForOfStatement = "ForOfStatement"
    ForInStatement = "ForInStatement"
    FunctionDeclaration = "FunctionDeclaration"
    FunctionExpression = "FunctionExpression"
    Identifier = "Identifier"
    IfStatement = "IfStatement"
    Import = "Import"
    ImportDeclaration = "ImportDeclaration"
    ImportDefaultSpecifier = "ImportDefaultSpecifier"
    ImportNamespaceSpecifier = "ImportNamespaceSpecifier"
    ImportSpecifier = "ImportSpecifier"
    Literal = "Literal"
    LabeledStatement = "LabeledStatement"
    LogicalExpression = "LogicalExpression"
    MemberExpression = "MemberExpression"
    MetaProperty = "MetaProperty"
    MethodDefinition = "MethodDefinition"
    FieldDefinition = "FieldDefinition"
    NewExpression = "NewExpression"
    ObjectExpression = "ObjectExpression"
    ObjectPattern = "ObjectPattern"
    Program = "Program"
    Property = "Property"
    RestElement = "RestElement"
    ReturnStatement = "ReturnStatement"
    SequenceExpression = "SequenceExpression"
    SpreadElement = "SpreadElement"
    Super = "Super"
    SwitchCase = "SwitchCase"
    SwitchStatement = "SwitchStatement"
    TaggedTemplateExpression = "TaggedTemplateExpression"
    TemplateElement = "TemplateElement"
    TemplateLiteral = "TemplateLiteral"
    ThisExpression = "ThisExpression"
    ThrowStatement = "ThrowStatement"
    TryStatement = "TryStatement"
    UnaryExpression = "UnaryExpression"
    UpdateExpression = "UpdateExpression"
    VariableDeclaration = "VariableDeclaration"
    VariableDeclarator = "VariableDeclarator"
    WhileStatement = "WhileStatement"
    WithStatement = "WithStatement"
    YieldExpression = "YieldExpression"

    ArrowParameterPlaceHolder = "ArrowParameterPlaceHolder"
    BlockComment = "BlockComment"
    LineComment = "LineComment"
