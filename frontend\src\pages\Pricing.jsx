import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../hooks/useAuth'
import { FiCheck, FiX } from 'react-icons/fi'
import toast from 'react-hot-toast'
import PaymentModal from '../components/PaymentModal'
import { getSubscriptionStatus } from '../api'
import PageContainer from '../components/layout/PageContainer'

const plans = [
  {
    name: 'Free',
    price: 0,
    currency: 'USD',
    description: 'Perfect for getting started',
    features: [
      '150 tokens per day',
      'Basic code generation',
      'Community support',
      'Standard templates',
      'Token repurchase available'
    ],
    buttonText: 'Get Started',
    highlighted: false,
    planId: 'free'
  },
  {
    name: 'Pro',
    price: 30,
    currency: 'USD',
    period: '/month',
    description: 'For professional developers',
    features: [
      '500 tokens per day',
      'Advanced code generation',
      'Image generation',
      'Live preview',
      'Priority support',
      'API access',
      'Token repurchase available'
    ],
    buttonText: 'Upgrade to Pro',
    highlighted: true,
    planId: 'pro'
  },
  {
    name: 'Enterprise',
    price: 299,
    currency: 'USD',
    period: '/user/month',
    description: 'For large organizations',
    features: [
      '1000 tokens per day per user',
      'Unlimited code generation',
      'Custom integrations',
      'Dedicated support',
      'SLA guarantees',
      'Team collaboration',
      'Advanced analytics',
      'Token repurchase available'
    ],
    buttonText: 'Contact Sales',
    highlighted: false,
    planId: 'enterprise'
  },
]

export default function Pricing() {
  const { user, token } = useAuth()
  const isAuthenticated = !!token
  const navigate = useNavigate()
  const [currentPlan, setCurrentPlan] = useState('free')
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState(null)
  const [subscription, setSubscription] = useState(null)

  useEffect(() => {
    if (isAuthenticated) {
      fetchSubscriptionStatus()
    }
  }, [isAuthenticated])

  const fetchSubscriptionStatus = async () => {
    try {
      const status = await getSubscriptionStatus()
      setSubscription(status)
      setCurrentPlan(status.plan || 'free')
    } catch (error) {
      console.error('Failed to fetch subscription status:', error)
    }
  }

  const handlePlanSelect = async (planId) => {
    if (!isAuthenticated) {
      navigate('/login')
      return
    }

    const planData = plans.find(p => p.planId === planId)

    if (planId === 'free') {
      toast.info('You are already on the free plan')
      return
    }

    if (planId === 'enterprise') {
      // For enterprise, redirect to contact page
      navigate('/contact')
      toast.info('Please contact our sales team for Enterprise pricing')
      return
    }

    if (planData.price === 0) {
      toast.info('This plan is free!')
      return
    }

    setSelectedPlan(planData)
    setShowPaymentModal(true)
  }

  const handlePaymentSuccess = (planName) => {
    setCurrentPlan(planName)
    setShowPaymentModal(false)
    fetchSubscriptionStatus()
    toast.success(`Successfully upgraded to ${planName} plan!`)
  }

  return (
    <PageContainer>
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] py-32 px-4 sm:px-6 lg:px-8 text-white font-sans" style={{ fontFamily: 'Inter, Space Grotesk, sans-serif' }}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-7xl font-extrabold bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 text-transparent bg-clip-text drop-shadow-glow tracking-tight mb-6">
              Simple, Transparent Pricing
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 font-light">
              Choose the plan that best fits your development needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {plans.map((plan) => (
              <div
                key={plan.name}
                className={`relative bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border rounded-3xl p-8 flex flex-col ${
                  plan.highlighted
                    ? 'border-cyan-500/50 shadow-2xl shadow-cyan-500/20 scale-105'
                    : 'border-gray-700/50'
                } hover:border-cyan-500/50 transition-all duration-300 hover:transform hover:scale-105`}
              >
                {plan.highlighted && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-6 py-2 rounded-full text-sm font-bold">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                  <p className="text-gray-300 mb-6">{plan.description}</p>

                  <div className="mb-6">
                    {plan.price === 0 ? (
                      <span className="text-4xl font-bold text-cyan-400">Free</span>
                    ) : (
                      <>
                        <span className="text-4xl font-bold text-white">${plan.price}</span>
                        {plan.period && (
                          <span className="text-gray-400 text-lg">{plan.period}</span>
                        )}
                        <div className="text-sm text-gray-400 mt-1">USD</div>
                      </>
                    )}
                  </div>

                  <button
                    onClick={() => handlePlanSelect(plan.planId)}
                    className={`w-full py-4 px-6 rounded-xl font-bold text-lg transition-all duration-300 ${
                      currentPlan === plan.planId
                        ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                        : plan.highlighted
                        ? 'bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white transform hover:scale-105 shadow-lg shadow-cyan-500/25 animate-pulse'
                        : plan.planId === 'enterprise'
                        ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white transform hover:scale-105'
                        : 'border-2 border-gray-600 hover:border-cyan-400 text-gray-300 hover:text-white hover:bg-cyan-400/10'
                    }`}
                    disabled={currentPlan === plan.planId}
                  >
                    {currentPlan === plan.planId ? 'Current Plan' : plan.buttonText}
                  </button>
                </div>

                <div className="flex-1">
                  <h4 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-4">
                    What's included
                  </h4>
                  <ul className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-3">
                        <FiCheck className="h-5 w-5 text-green-400 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>

          {/* Token Repurchase Section */}
          <div className="mt-20 text-center">
            <div className="bg-gradient-to-r from-orange-900/20 to-red-900/20 border border-orange-900/30 rounded-3xl p-8 max-w-4xl mx-auto mb-16">
              <h2 className="text-3xl font-bold text-white mb-4">Need More Tokens?</h2>
              <p className="text-xl text-gray-300 mb-6">
                Exhausted your daily tokens? Purchase additional tokens instantly to continue your development workflow.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6">
                  <h3 className="text-xl font-bold text-orange-400 mb-2">100 Tokens</h3>
                  <p className="text-3xl font-bold text-white mb-2">$5</p>
                  <p className="text-gray-400 text-sm">USD</p>
                </div>
                <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-orange-500/50 rounded-2xl p-6 scale-105">
                  <h3 className="text-xl font-bold text-orange-400 mb-2">250 Tokens</h3>
                  <p className="text-3xl font-bold text-white mb-2">$10</p>
                  <p className="text-gray-400 text-sm">USD • Best Value</p>
                </div>
                <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6">
                  <h3 className="text-xl font-bold text-orange-400 mb-2">500 Tokens</h3>
                  <p className="text-3xl font-bold text-white mb-2">$18</p>
                  <p className="text-gray-400 text-sm">USD</p>
                </div>
              </div>
              <p className="text-gray-400 text-sm mt-6">
                Additional tokens are available for all plans and expire after 30 days if unused.
              </p>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mt-20 text-center">
            <h2 className="text-3xl font-bold text-white mb-8">Frequently Asked Questions</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6">
                <h3 className="text-lg font-bold text-cyan-400 mb-3">Can I change plans anytime?</h3>
                <p className="text-gray-300">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
              </div>
              <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6">
                <h3 className="text-lg font-bold text-cyan-400 mb-3">What payment methods do you accept?</h3>
                <p className="text-gray-300">We accept all major credit cards, debit cards, UPI, and net banking through Razorpay. All prices are in USD.</p>
              </div>
              <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6">
                <h3 className="text-lg font-bold text-cyan-400 mb-3">Can I buy additional tokens?</h3>
                <p className="text-gray-300">Yes! If you exhaust your daily tokens, you can purchase additional tokens instantly. Additional tokens expire after 30 days.</p>
              </div>
              <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6">
                <h3 className="text-lg font-bold text-cyan-400 mb-3">Is there a free trial?</h3>
                <p className="text-gray-300">Yes! The free plan gives you 150 tokens daily to try all basic features before upgrading.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Modal */}
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          plan={selectedPlan}
          onSuccess={handlePaymentSuccess}
        />

        <style>{`.drop-shadow-glow { filter: drop-shadow(0 0 6px #00fff7cc); }`}</style>
      </div>
    </PageContainer>
  )
} 