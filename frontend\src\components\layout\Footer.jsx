import { Link } from 'react-router-dom'
import { <PERSON>G<PERSON><PERSON>, <PERSON>Twi<PERSON>, FiLinkedin } from 'react-icons/fi'

const footerLinks = {
  Company: [
    { name: 'About Us', path: '/about' },
    { name: 'Contact', path: '/contact' },
    { name: 'Pricing', path: '/pricing' },
    { name: 'FAQ', path: '/faq' },
  ],
  Product: [
    { name: 'Code Generation', path: '/generate' },
    { name: 'API Documentation', path: '/api-docs' },
    { name: 'Code Analysis', path: '/analyze' },
    { name: 'Dashboard', path: '/dashboard' },
  ],
  Legal: [
    { name: 'Privacy Policy', path: '/privacy' },
    { name: 'Terms of Service', path: '/terms' },
    { name: 'Cookie Policy', path: '/cookies' },
  ],
  Support: [
    { name: 'Help Center', path: '/faq' },
    { name: 'API Docs', path: '/api-docs' },
    { name: 'Community', path: '/forum' },
    { name: 'Status', path: '/status' },
  ],
}

export default function Footer() {
  return (
    <footer className="bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] text-gray-300 border-t border-gray-800/50">
      <div className="max-w-7xl mx-auto px-6 py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-10 mb-12">
          {Object.entries(footerLinks).map(([section, links]) => (
            <div key={section}>
              <h4 className="text-lg font-bold text-white mb-6 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
                {section}
              </h4>
              <ul className="space-y-3">
                {links.map((link) => (
                  <li key={link.name}>
                    <Link
                      to={link.path}
                      className="text-gray-400 hover:text-cyan-400 transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Newsletter Section */}
        <div className="bg-gradient-to-r from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 mb-12">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-4">Stay Updated with VIKKI AI</h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Get the latest updates on new features, AI models, and development tools.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 bg-gray-800/50 border border-gray-600 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-400"
              />
              <button className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-8 py-3 rounded-xl font-bold transition-all duration-300 transform hover:scale-105">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700/50 pt-8 flex flex-col md:flex-row justify-between items-center gap-6">
          <div className="flex items-center gap-6">
            <Link to="/" className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">V</span>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
                VIKKI AI
              </span>
            </Link>
          </div>

          <div className="flex gap-6">
            <a
              href="https://github.com/vikki-ai"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-cyan-400 transition-colors duration-200"
            >
              <FiGithub className="w-6 h-6" />
            </a>
            <a
              href="https://twitter.com/vikki_ai"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-cyan-400 transition-colors duration-200"
            >
              <FiTwitter className="w-6 h-6" />
            </a>
            <a
              href="https://linkedin.com/company/vikki-ai"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-cyan-400 transition-colors duration-200"
            >
              <FiLinkedin className="w-6 h-6" />
            </a>
          </div>

          <p className="text-sm text-gray-400 text-center">
            &copy; {new Date().getFullYear()} VIKKI AI. Made with ❤️ for developers worldwide.
          </p>
        </div>
      </div>
    </footer>
  )
}
