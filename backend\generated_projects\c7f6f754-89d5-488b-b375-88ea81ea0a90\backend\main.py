from fastapi import FastAPI
from pydantic import BaseModel
from motor.motor_asyncio import AsyncIOMotorClient
app = FastAPI()
client = AsyncIOMotorClient("mongodb://localhost:27017/")
db = client["my_soap_store"]
class Item(BaseModel):
name: str
description: str
price: float
class Config:
orm_mode = True
@app.get("/items/{item_id}")
async def read_item(item_id: int, q: str = None):
item_query = {"id": item_id}
if q:
item_query["name"] = {"$regex": q, "$options": "i"}
item = await db.items.find_one(item_query)
return item
if __name__ == "__main__":
import uvicorn
uvicorn.run(app, host="127.0.0.1", port=8000)